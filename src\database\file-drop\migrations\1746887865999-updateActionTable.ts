import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateActionTable1746887865999 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action 
            MODIFY COLUMN locked_until DATETIME NULL;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action 
            MODIFY COLUMN locked_until DATETIME NOT NULL;
        `);
    }
}
