import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivityLog } from './entities/activity-log.entity';
import { CompanyConfig } from './entities/company-config.entity';
import { ContactMasterList } from './entities/contact-master-list.entity';
import { ContactWasherApiCompletedFiles } from './entities/contact-washer-api-completed-files.entity';
import { Job } from './entities/job.entity';
import { JobAction } from './entities/job-action.entity';
import { JobCampaign } from './entities/job-campaign.entity';
import { JobDetail } from './entities/job-detail.entity';
import { JobDetailArchive } from './entities/job-detail-archive.entity';
import { JobDetailStatus } from './entities/job-detail-status.entity';
import { JobFiles } from './entities/job-files.entity';
import { JobStats } from './entities/job-stats.entity';
import { JobStatus } from './entities/job-status.entity';
import { JobType } from './entities/job-type.entity';
import { UpdateSource } from './entities/update-source.entity';
import { WhatsappStatus } from './entities/whatsapp-status.entity';
import { fileDropDataSource } from './typeorm.config';
import { JobStatsService } from './services/job-stats.service';
import { JobActionsService } from './services/job-actions.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ActivityLog,
      CompanyConfig,
      ContactMasterList,
      ContactWasherApiCompletedFiles,
      Job,
      JobAction,
      JobCampaign,
      JobDetail,
      JobDetailArchive,
      JobDetailStatus,
      JobFiles,
      JobStats,
      JobStatus,
      JobType,
      UpdateSource,
      WhatsappStatus,
    ],fileDropDataSource),
  ],
  providers:[JobStatsService, JobActionsService],
  exports: [TypeOrmModule, JobStatsService, JobActionsService],
})
export class DatabaseModule {}