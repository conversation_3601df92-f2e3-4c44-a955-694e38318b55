import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

const MigrationDataSource = new DataSource({
  type: 'mysql',
  host: process.env.FILE_DROP_DB_HOST,
  port: parseInt(process.env.FILE_DROP_DB_PORT || '3306', 10),
  username: process.env.FILE_DROP_DB_USERNAME,
  password: process.env.FILE_DROP_DB_PASSWORD,
  database: process.env.FILE_DROP_DB_DATABASE,
  entities: [__dirname + '/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '../**/migrations/y*{.ts,.js}'],
  synchronize: false,
  migrationsRun: true,
});

export default MigrationDataSource;