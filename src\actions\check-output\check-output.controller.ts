import { Controller, Get, Query } from '@nestjs/common';
import { CheckOutputService } from './check-output.service';
import { CompareFilesResponseDto } from './dtos/compare-files-response.dto';

@Controller('check-output')
export class CheckOutputController {
  constructor(private readonly checkOutputService: CheckOutputService) {}

  @Get('compare')
  async compareFiles(
    @Query('jobId') jobId: string,
    @Query('fileId') fileId: string
  ): Promise<CompareFilesResponseDto> {
    console.log(`Comparing files for Job ID: ${jobId} and File ID: ${fileId}`);
    
    return this.checkOutputService.compareFiles(jobId, fileId);
  }
}