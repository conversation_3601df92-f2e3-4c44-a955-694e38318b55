import { <PERSON>, <PERSON>, <PERSON>m, Req } from '@nestjs/common';
import { JobDetailStatus, JobSubStatusEnum, WhatsAppStatus } from 'src/database/file-drop/entities/enums';
import { ContactService as FlexContactService } from 'src/database/flex/services/contact.service';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import * as libphonenumber from 'google-libphonenumber';
import { ChannelService } from 'src/database/flex/services/channel.service';
import { JobAction } from 'src/database/file-drop/entities/job-action.entity';
import { CampaignService } from 'src/database/flex/services/campaign.service';
import { TemplateGroupsService } from 'src/database/flex/services/template-groups.service';
import axios from 'axios';
import { CampaignAudienceService } from 'src/database/flex/services/campaign-audience.service';
import { CampaignAudience } from 'src/database/flex/entities/campaign-audience.entity';
import { MessageService } from 'src/database/flex/services/message.service';
import { Templates } from 'src/database/flex/entities/templates.entity';
import { TemplateService } from 'src/database/flex/services/template.service';
import { JobActionsService } from 'src/database/file-drop/services/job-actions.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { Action } from 'rxjs/internal/scheduler/Action';
import Bottleneck from 'bottleneck';
import { JobStatsService } from 'src/database/file-drop/services/job-stats.service';

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
const TEMPLATE_MESSAGE_TYPE = "2";
const WHATSAPP_CHANNEL_TYPE = "1";

const CAMPAIGN_IN_PROGRESS = "1";
const CAMPAIGN_IN_SCHEDULED = "2";

const CAMPAIGN_TYPE_TEMPLATE = "3";

const MESSAGE_SCHEDULED_STATE_ID = "3";

@Controller('actions/:actionId/flex-create-campaign')
export class FlexCreateCampaignController { 
constructor(
        private readonly actionProcessingService: ActionProcessingService,
        private readonly flexContactService: FlexContactService,
        private readonly jobDetailsService: JobDetailsService,
        private readonly channelService: ChannelService,
        private readonly campaignService: CampaignService,
        private readonly templateGroupsService: TemplateGroupsService,
        private readonly campaignAudienceService: CampaignAudienceService,
        private readonly messageService: MessageService,
        private readonly templateService: TemplateService,
        private readonly jobService : JobService,
        private readonly jobsStatsService: JobStatsService,
  ) {}

  @Post('')
  async createCampaign(@Req() req, @Param('actionId') actionId: string): Promise<void> {
    const validStatusIds : string[] = [JobDetailStatus.Inserted.toString(), JobDetailStatus.PreProcessingSkippedDueToCutOffTime.toString(), JobDetailStatus.WashedViaApi.toString()]

    await this.actionProcessingService.process(actionId, async (action) => {

      const channel = await this.channelService.findOneByHelpDeskId(action.data.channelId);

      if (!channel) {
        throw new Error(`Channel with ID ${action.data.channelId} not found`);
      }

      const {templateId, hasMedia, templateBodyVariables} = await this.getTemplateDetails(action, channel.id)


      const bodyVaryables = {}

      for(const variable of templateBodyVariables) {
        bodyVaryables[variable] = variable;
      }
    
      const variables = {body:bodyVaryables};
      const campaignName = action.job.original_file_name.split("/")[2]??action.job.original_file_name;
      
      let campaign = await this.campaignService.findOneByName(campaignName, action.job.company_id);


      if (!campaign) {
        const firstEntry = await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 1, 0, null, null);
        const mediaUrl = firstEntry[0].row_data["mediaUrl"] ?? "";
        let mediaType = "";

        console.log("firstEntry", firstEntry);
  
        if (mediaUrl) {
          try {
            const response = await axios.head(mediaUrl);
            mediaType = response.headers['content-type'] || "";
          } catch (error) {
            console.error(`Failed to fetch media type for URL ${mediaUrl}:`, error.message);
          }
        } 
        else if (hasMedia)
        {
          if (!mediaType) {
            throw new Error(`Media type not found in file`);
          }
        }

        campaign = await this.campaignService.create({
          name: campaignName,
          channelId: channel.id,
          userId: action.job.created_by_user_id,
          mediaUrl: mediaUrl,
          companyId: action.job.company_id,
          messageTypeId: TEMPLATE_MESSAGE_TYPE,
          variables: JSON.stringify(variables),
          mediaType: mediaType,
          channelType: WHATSAPP_CHANNEL_TYPE,
          campaignDuration: 'ONE_MONTH',
          campaignStatusId: CAMPAIGN_IN_SCHEDULED,
          templateId : templateId,
          reverseBilling: false,
          campaignTypeId: CAMPAIGN_TYPE_TEMPLATE,
          sentAt: action.data.scheduleDate,
          scheduledFor:action.data.scheduleDate
        });
      }

      await this.jobService.addDataToJob(action.job, {campaignId: campaign.id});

      await this.addMessagesToCampaign(action,templateBodyVariables, campaign.id, campaign.channelId, validStatusIds);
      //await this.addContactsToCampaign(action, campaign.id, validStatusIds);
      //await this.campaignService.InsertMessagesForCampaign(campaign.id);
    }, JobSubStatusEnum['Creating Campaign']);     
  }

  private async getTemplateDetails(action: JobAction,channelId: string ) :Promise<{templateId: string, hasMedia: boolean, templateBodyVariables: string[]}> 
  {
    const templateGroup = await this.templateGroupsService.findOneByName(action.data.templateGroup, action.job.company_id);
    let firstTemplate : Templates;
    let templateBodyVariables: string[] = [];
    
    if (!action.data.templateGroup){
      throw new Error(`Template / Template group Name not found in action Data`);
    }

    if (templateGroup) {
      console.info(`Template Group ${JSON.stringify(templateGroup)}`);
      firstTemplate = templateGroup.templates[0];      
      templateBodyVariables = templateGroup?.variableGroup.split(",");
    }
    else{
      console.info(`Template group with ID ${action.data.templateGroup} not found`);

      firstTemplate = await this.templateService.findOneByName(action.data.templateGroup, channelId);

      const componentsString = firstTemplate.components;
      const components : any[] = JSON.parse(componentsString);
      
      const bodyComponent = components.find(c => c.type?.toUpperCase() === "BODY");
      // Extract all numbers inside the pattern {{Number}} that can appear anywhere in the text
      const bodyText = bodyComponent?.text || "";
      const matches = bodyText.match(/{{(\d+)}}/g) || [];
      templateBodyVariables = matches.map(match => match.replace(/[^\d]/g, ''));




      console.log("firstTemplate", firstTemplate);
      console.log(`Found using template name ${action.data.templateGroup} in channel ${channelId}`);

      // firstTemplate?.split(",");
    }

    if (!firstTemplate) {
      throw new Error(`Template with ID ${templateGroup.templates[0].id} not found`);
    }

    
    const hasMedia = firstTemplate.hasMedia;

    return {templateId: firstTemplate.id, hasMedia,  templateBodyVariables}
  }

  private async addMessagesToCampaign(action: JobAction, templateBodyParams :string[], campaignId: string, channelId: string, validStatusIds: string[]) {
    let batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 500, 0, validStatusIds, null);
    const audiencesId = action.data.audiencesId;
    let stats = await this.jobsStatsService.findOne(action.job_id);
    let percentageCompleteAtStart = action.job.percentage_complete;

    while (batchToProcess.length > 0) {

      if (stats.total_details){
        console.log(`${batchToProcess[0].source_row_index} / ${stats.total_details}) * ${action.percentage_of_job}`);
        const newPercentageComplete = percentageCompleteAtStart + Math.floor((batchToProcess[0].source_row_index / stats.total_details) * action.percentage_of_job);

        await this.jobService.update(action.job_id, {
            percentage_complete: newPercentageComplete
        });
      }
    

      // Set up Bottleneck for parallel processing (10 at a time)
      const limiter = new Bottleneck({ maxConcurrent: 10 });

      await Promise.all(batchToProcess.map(jobDetail =>
        limiter.schedule(async () => {
          const bodyParams: string[] = [];
          for (const param of templateBodyParams) {
            const paramValue = jobDetail.row_data[param];
            bodyParams.push(paramValue);
          }
          const message = await this.messageService.upsert({
            campaignId: campaignId,
            channelId: channelId,
            messageStateId: MESSAGE_SCHEDULED_STATE_ID,
            contactNumber: jobDetail.sanitized_number,
            contactId: jobDetail.contact_id,
            messageTypeId: TEMPLATE_MESSAGE_TYPE,
            channelTypeId: WHATSAPP_CHANNEL_TYPE,
            scheduledFor: action.data.scheduleDate,
            sentAt: action.data.scheduleDate,
            bodyParams: bodyParams.join(',')
          });

          if (message) {
            jobDetail.job_detail_status_id = JobDetailStatus.Successful.toString();
          } else {
            jobDetail.job_detail_status_id = JobDetailStatus.FailedToAddToCampaign.toString();
          }
          await this.jobDetailsService.update(jobDetail.id, { job_detail_status_id: jobDetail.job_detail_status_id, message_id: message.id });
        })
      ));
    
      batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 500, 0, validStatusIds, null);
    }
  }

  /*
  private async addContactsToCampaign(action: JobAction, campaignId: string, validStatusIds: string[]) {
    let lastRowIndex = 0;
    let batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 100, lastRowIndex, validStatusIds, null);
    const audiencesId = action.data.audiencesId;

    while (batchToProcess.length > 0) {

      if (batchToProcess.length === 0) {
        break;
      }

      const conatactToAddToAudience :Partial<CampaignAudience>[] = [];

      for (const jobDetail of batchToProcess) {
        conatactToAddToAudience.push({
          campaignId: campaignId,
          contactId: jobDetail.contact_id,
        });  
        
        jobDetail.job_detail_status_id = JobDetailStatus.Successful.toString();
      }    


      const audience = await this.campaignAudienceService.createBatch(conatactToAddToAudience);

      for (const entry of audience) {
        const jobDetail = batchToProcess.find((detail) => detail.contact_id === entry.contactId);

        if (jobDetail) {
          jobDetail.job_detail_status_id = JobDetailStatus.Successful.toString();
        }
      }

      const failedJobDetails = batchToProcess.filter((detail) => detail.job_detail_status_id !== JobDetailStatus.Successful.toString());
      for (const jobDetail of failedJobDetails) {
        jobDetail.job_detail_status_id = JobDetailStatus.FailedToAddToCampaign.toString();
      }

      this.jobDetailsService.updateBatch(batchToProcess);

      batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 100, lastRowIndex, validStatusIds, null);
    }
  }*/
}