import { MigrationInterface, QueryRunner } from "typeorm";

export class JobTypeAddFlexWebhookOptputUpdate1748383830900 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(`
            insert into job_type (id, name)
values (9, 'Flex Web Hook - Optout Updates');
        `);

        queryRunner.query(`
insert into job_action_type (id, name)
values (11, 'Flex Web Hook - Optout Updates')
        `);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(`
            delete from job_type where id = 9;
        `);

        queryRunner.query(`
            delete from job_action_type where id = 11;
        `);
    }

}
