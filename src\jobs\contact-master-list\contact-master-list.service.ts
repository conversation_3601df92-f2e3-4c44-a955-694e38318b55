import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactMasterList } from '../../database/file-drop/entities/contact-master-list.entity';
import { WhatsAppStatus } from '../../database/file-drop/entities/enums';

@Injectable()
export class ContactMasterListService {
  constructor(
    @InjectRepository(ContactMasterList)
    private readonly contactMasterListRepository: Repository<ContactMasterList>,
  ) {}

  private whatsapp_status_ids = Object.values(WhatsAppStatus).map(String);

  async findAll(): Promise<ContactMasterList[]> {
    return this.contactMasterListRepository.find();
  }

  async findOne(id: string): Promise<ContactMasterList> {
    return this.contactMasterListRepository.findOneBy({ phone_number: id });
  }

  async create(contact: ContactMasterList): Promise<ContactMasterList> {
    return this.contactMasterListRepository.save(contact);
  }

  async update(id: string, contact: Partial<ContactMasterList>): Promise<void> {
    await this.contactMasterListRepository.update(id, contact);
  }

  async remove(id: string): Promise<void> {
    await this.contactMasterListRepository.delete(id);
  }

  async upsert(data: any | any[]): Promise<void> {
    try{
      console.log('Data to upsert:', data);

    await this.contactMasterListRepository.upsert(data, {
      conflictPaths: ['phone_number'], 
    });
  }
  catch (error) {
    console.error('Error during upsert:', error);
    console.log(`Data: ${JSON.stringify(data)}`);
    throw error; // Rethrow the error to handle it in the calling function if needed
  }
}

  createEntityObject(rowData, rowIndex): Partial<ContactMasterList> | null {
    const entityObject: Partial<ContactMasterList> = {
      phone_number: rowData.phone_number || '',
      update_source_id: rowData.update_source_id || null,
      whatsapp_status_date: rowData.whatsapp_status_date || null,
      whatsapp_status_id: rowData.whatsapp_status_id || null,
    };

    const isValid = this.validateEntityObject(entityObject, rowIndex);

    if (isValid) {
      return entityObject;
    } else {
      return null;
    }
  }



  validateEntityObject(rowData : Partial<ContactMasterList> , rowIndex) {
    if (!rowData.phone_number) {
      console.error(`Row ${rowIndex}: Phone Number is Blank, FullData: ${JSON.stringify(rowData)}`);
      return false;
    }

    if (!this.whatsapp_status_ids.includes(rowData.whatsapp_status_id)) {
      console.error(`Row ${rowIndex}: Invalid WhatsAppStatus - ${rowData.whatsapp_status_id}, FullData: ${JSON.stringify(rowData)}`);
      return false;
    }

    return true;
  }
}