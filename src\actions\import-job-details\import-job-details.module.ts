import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobDetail } from '../../database/file-drop/entities/job-detail.entity';
import { JobFiles } from 'src/database/file-drop/entities/job-files.entity';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { CompanyConfig } from 'src/database/file-drop/entities/company-config.entity';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';
import { CompanyConfigModule } from 'src/jobs/company-config/company-config.module';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { JobDetailsFileImportService } from './job-details-file-import.service';
import { ImportJobDetailsController } from './import-job-details.controller';
import { JwtService } from '@nestjs/jwt';
import { XlsxStreamProcessor } from 'src/utils/xlxs-stream-processors';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { JobActionsService } from 'src/database/file-drop/services/job-actions.service';
import { EngineModule } from 'src/engine/engine.module';

@Module({
  imports: [DatabaseModule, JobFilesModule, CompanyConfigModule, EngineModule],
  controllers: [ImportJobDetailsController],
  providers: [JobDetailsService, JobService,JobDetailsFileImportService,GcpBucketService,CsvStreamProcessor, XlsxStreamProcessor,JwtService,JobActionsService],
  exports: [JobDetailsService],
})
export class ImportJobDetailsModule {}