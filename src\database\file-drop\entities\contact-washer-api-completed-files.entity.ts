import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';

@Entity('contact_washer_api_completed_files')
export class ContactWasherApiCompletedFiles {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  clean_file_url: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @Column({ type: 'varchar', length: 500, nullable: true })
  file_name: string;

  @Column({ type: 'varchar', length: 5000, nullable: true })
  washer_url: string;

  @Column({ type: 'tinyint', nullable: true })
  recieved_from_washer: boolean;

  @Column({ type: 'varchar', length: 5000, nullable: true })
  sent_to_washer: string;
}