import { Injectable } from '@nestjs/common';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import * as readline from 'readline';
import { ConfigService } from '@nestjs/config';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { Readable } from 'stream';
import { MemoryUtil } from 'src/utils/memory.util';
import { CompareFilesResponseDto } from './dtos/compare-files-response.dto';

@Injectable()
export class CheckOutputService {
  constructor(
    private readonly jobService: JobService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService,
    private readonly jobFileService: JobFilesService,
  ) {}

  async compareFiles(jobId: string, fileId: string): Promise<CompareFilesResponseDto> {
    console.log(`Comparing files for Job ID: ${jobId} and File ID: ${fileId}`);

    const batchSize = 1000; // Define the batch size for reading lines

    // Fetch job details to get the original file name
    const job = await this.jobService.findOneWithoutCompanyCheck(jobId); // Replace 'companyIdPlaceholder' with the actual company ID
    const bucketName = process.env.GCP_BUCKET_NAME;

    const origonalFile = await this.jobFileService.findOrigonalFileForJob(jobId);
    const originalFileStream = await this.gcpBucketService.getFileStream(bucketName, `import/${job.company_id}/${origonalFile.name}`);

    // Open streams for the original file and the second file
    const secondFile = await this.jobFileService.findOne(fileId);
    const secondFileStream = await this.gcpBucketService.getFileStream(bucketName, `export/${job.company_id}/${secondFile.name}`);


    // Create line readers for both files
    const originalFileReader = readline.createInterface({ input: originalFileStream });
    const secondFileReader = readline.createInterface({ input: secondFileStream });
    const stats = {
        headersAdded:'',
        linesProcessed:0,
        fileNameOrigonalFile: origonalFile.name,
        fileNameExportFile: secondFile.name,
        totalLinesOrigonalFile:0,
        totalLinesExportFile:0,
        matchingLines:0,
        mismatch: []
    }



    const origonalFileLines = [];
    const compareFileLines = [];

    // Add a flag to track if the stream is closed
    let originalStramPaused = false;
    //let secondStreamPaused = false;

    let processingInterval = false;

    // Modify the interval to check if the stream is still open before resuming
    const interval = setInterval(async () => {
        /*
      if (origonalFileLines.length < batchSize && originalStramPaused && originalFileStream.isPaused  && !originalFileStream.destroyed) {
        console.log('Resuming original file stream...');
        originalStramPaused = false;        
        originalFileStream.resume();
      }

      if (compareFileLines.length < batchSize && secondStreamPaused && secondFileStream.isPaused &&  !secondFileStream.destroyed) {
        console.log('Resuming second file stream...');
        secondStreamPaused = false;
        secondFileStream.resume();
      }
        */
        if (processingInterval)
            return;

        processingInterval = true;

        await MemoryUtil.pauseIfHighMemory();

       if (originalStramPaused && originalFileStream.isPaused() && secondFileStream.isPaused()) {
            this.compaireLInes(origonalFileLines, compareFileLines, stats);
            originalFileReader.resume(); // Pause reading to prevent memory overflow
            secondFileReader.resume(); 
            originalStramPaused = false; // Reset the flag
       }

       processingInterval = false;

    }, 10); // Check every 100ms


    // Create promises to wait for both file readers to complete
    const originalFilePromise = (async () => {
      for await (const line of originalFileReader) {
        stats.totalLinesOrigonalFile++;
        origonalFileLines.push(line);

        if (origonalFileLines.length > batchSize && compareFileLines.length > batchSize) {
        //  console.log('Pausing original file stream...');
        //  originalStramPaused = true;
          originalFileReader.pause(); // Pause reading to prevent memory overflow
          secondFileReader.pause(); 
          originalStramPaused = true;
        }
      }
    })();

    const secondFilePromise = (async () => {
      for await (const line of secondFileReader) {
        stats.totalLinesExportFile++;
        compareFileLines.push(line);

 /*       if (compareFileLines.length > batchSize) {
          console.log('Pausing second file stream...');
          secondFileReader.pause(); // Pause reading to prevent memory overflow
        }*/
      }
    })();

    // Wait for both file readers to complete
    await Promise.all([originalFilePromise, secondFilePromise]);

    // process any remaining lines in the sets
    this.compaireLInes(origonalFileLines, compareFileLines, stats);

    clearInterval(interval);

    return stats;
  }

  private compaireLInes(origonalFileLines: string[], compareFileLines: string[], stats: any)  {
    const smallerBatchSize = Math.min(origonalFileLines.length, compareFileLines.length);
    
    for (let i=0; i < smallerBatchSize; i++) {   
        stats.linesProcessed++;          
        const match = compareFileLines[i].startsWith(origonalFileLines[i]);
        
        if (match) {
            stats.matchingLines++;
        }
        else{
            stats.mismatch.push({index: i, line1: origonalFileLines[i], line2: compareFileLines[i]});
        }

        if (match && stats.linesProcessed === 1)
        {
            stats.headersAdded = compareFileLines[i].substring(origonalFileLines[i].length);
        }
    }

    console.log(`Processed ${stats.linesProcessed} lines...`);

    // Remove processed entries from the arrays
    origonalFileLines.splice(0, smallerBatchSize);
    compareFileLines.splice(0, smallerBatchSize);
  }
}


