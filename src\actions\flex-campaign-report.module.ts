import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { EngineModule } from 'src/engine/engine.module';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';

import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { UtilModule } from 'src/utils/util.module';
import { FlexCampaignReportController } from './flex-campaign-report.controller';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';
import { CompanyConfigModule } from 'src/jobs/company-config/company-config.module';


@Module({
      imports: [DatabaseModule, EngineModule, FlexDatabaseModule, UtilModule, JobFilesModule, CompanyConfigModule],
  controllers: [FlexCampaignReportController],
  providers: [JobDetailsService,],
})
export class FlexCampaignReportModule {}