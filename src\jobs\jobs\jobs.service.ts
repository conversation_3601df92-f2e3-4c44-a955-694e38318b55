import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from '../../database/file-drop/entities/job.entity';
import { ListResponseDto } from 'src/shared/dtos/list-resposne.dto';

@Injectable()
export class JobService {


  findByOrigonalFileName(fileName: string, companyId: string): Promise<Job | null> {
    return this.jobRepository.findOne({ where: { original_file_name: fileName, company_id: companyId } });
  }
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
  ) {}

  async findAll(comapnyId: string): Promise<Job[]> {
    const jobs = await this.jobRepository.find({where : { company_id: comapnyId }});

    return jobs;
  }

  async findOne(id: string, companyId: string, relations:string[]): Promise<Job | null> {
    return this.jobRepository.findOne({ 
      where: { id, company_id: companyId },
      relations, // Preload the jobs actions
    });
  }

  async addDataToJob(job: Job, jobData: any): Promise<Job> {
    const jobFromDb = await this.jobRepository.findOne({ where: { id: job.id } });
    
    if (!jobFromDb.job_data)
    {
      jobFromDb.job_data = {};
    }

    jobFromDb.job_data = { ...jobFromDb.job_data, ...jobData };

    await this.jobRepository.update({ id: job.id }, { job_data: jobFromDb.job_data });
    
    return jobFromDb;
  }

  async findOneWithoutCompanyCheck(id: string): Promise<Job | null> {
    return this.jobRepository.findOne({ where: { id} });
  }

  async create(job: Partial<Job>): Promise<Job> {
    const newJob = this.jobRepository.create(job);
    return this.jobRepository.save(newJob);
  }

  async update(id: string, job: Partial<Job>): Promise<Job> {
    await this.jobRepository.update(id, job);
    return this.findOne(id, job.company_id, null);
  }

  async remove(id: string): Promise<void> {
    await this.jobRepository.delete(id);
  }

async updateJobHeaderRow(jobId: string, headerRow: string[]): Promise<void> {
    // Example logic to update header row for an existing job
    console.log('Updating header row for job:', jobId, 'with headers:', headerRow);

    // Find the job by ID
    const job = await this.jobRepository.findOne({ where: { id: jobId } });
    if (!job) {
        throw new Error(`Job with ID ${jobId} not found`);
    }

    // Update the headers
    job.file_headers = headerRow.join(',');
    await this.jobRepository.save(job);
}

  async updateJobData(job_id: string, newData: any) {
    const job = await this.jobRepository.findOne({ where: { id: job_id } });
    
    if (!job) {
      throw new Error(`Job with ID ${job_id} not found`);
    }
    job.job_data = { ...job.job_data, ...newData };

    return this.jobRepository.save(job);
  }

  async calculateStatus(jobId: string | null = null): Promise<void> {
    const query = `
      CALL job_stats_update(?);
    `;
    await this.jobRepository.query(query, [jobId]);

    const jobStatsQuery = `
      CALL job_calculate_status(?);
    `;
    await this.jobRepository.query(jobStatsQuery, [jobId]);
    
  }
}