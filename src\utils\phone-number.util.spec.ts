import { PhoneNumberUtil } from '../utils/phone-number.util';

describe('PhoneNumberUtil', () => {
  it('should validate and sanitize a valid phone number', () => {
    const result = PhoneNumberUtil.validateAndSanitize('+14155552671', 'US');
    expect(result.isValid).toBe(true);
    expect(result.sanitizedNumber).toBe('+14155552671');
  });

  it('should return invalid for an invalid phone number', () => {
    const result = PhoneNumberUtil.validateAndSanitize('12345', 'US');
    expect(result.isValid).toBe(false);
    expect(result.sanitizedNumber).toBeNull();
  });

  it('should handle errors gracefully', () => {
    const result = PhoneNumberUtil.validateAndSanitize('invalid-number', 'US');
    expect(result.isValid).toBe(false);
    expect(result.sanitizedNumber).toBeNull();
  });
});