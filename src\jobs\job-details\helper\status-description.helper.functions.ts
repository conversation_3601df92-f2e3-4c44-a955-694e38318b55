import { JobDetailStatus } from "src/database/file-drop/entities/enums";

export function getExportDetailForImport(exportDetail: string) {
    switch (exportDetail) {
      case JobDetailStatus.Inserted.toString():
        return 'Inserted';
      case JobDetailStatus.Duplicate.toString():
        return 'Duplicate';
      case JobDetailStatus.InvalidNumber.toString():
        return 'Invalid Number';
      case JobDetailStatus.FailedToImport.toString():
        return 'Failed to import';
      case JobDetailStatus.WashedViaDB.toString():
      case JobDetailStatus.WashedViaDB.toString():
        return 'Washed';
      case JobDetailStatus.ManualWashRequired.toString():
      case JobDetailStatus.ApiWashRequired.toString():
      case JobDetailStatus.SentToApi.toString():
        return 'Processing - Washing';      
      case JobDetailStatus.PreProcessingSkippedDueToCutOffTime.toString():
        return 'Processing';                       
      case JobDetailStatus.Paused.toString():
        return 'Paused';         
      case JobDetailStatus.Stopped.toString():
        return 'Stopped';       
      case JobDetailStatus.SentToApi.toString():
        return 'Processing - Washing';                                       
      case JobDetailStatus.SentToApi.toString():
        return 'Processing Complete';            
    }

    return 'Error';
  }