import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobActionImpovements1746768222012 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE \`job_action_type\` (
                \`id\` bigint unsigned NOT NULL AUTO_INCREMENT,
                \`name\` varchar(255) NOT NULL,
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` datetime DEFAULT NULL,
                PRIMARY KEY (\`id\`),
                UNIQUE KEY \`name\` (\`name\`)
            );
        `);

        await queryRunner.query(`
            INSERT INTO job_action_type (id, name)
            VALUES
            (1,'ImportJobDetailsFromFile'),
            (2,'DetectDuplicates'),
            (3,'WashWahatsAppNumbersData'),
            (4,'ImportContactsIntoFlex'),
            (5,'GenerateImportResultFiles');
        `);

        await queryRunner.query(`
            CREATE TABLE \`job_action_status\` (
                \`id\` bigint unsigned NOT NULL AUTO_INCREMENT,
                \`name\` varchar(255) NOT NULL,
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` datetime DEFAULT NULL,
                PRIMARY KEY (\`id\`),
                UNIQUE KEY \`name\` (\`name\`)
            );
        `);

        await queryRunner.query(`
            INSERT INTO job_action_status (id, name)
            VALUES
            (1,'Created'),
            (2,'Ready'),
            (3,'Processing'),
            (4,'completed'),
            (100,'Errored'),
            (101,'Paused'),
            (102,'Stopped');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP TABLE `job_action_status`;');
        await queryRunner.query('DROP TABLE `job_action_type`;');
    }

}
