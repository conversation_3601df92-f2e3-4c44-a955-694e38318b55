import { Controller, Post, Param, HttpException } from '@nestjs/common';
import { WashWhatsAppNumbersService } from './wash-whats-app-numbers.service';
import { WhatsAppWasherApiService } from 'src/utils/whats-app-washer-api';
import { ContactWasherApiCompletedFilesService } from 'src/jobs/contact-washer-api-completed-files/contact-washer-api-completed-files.service';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';
import { ContactMasterListService } from 'src/jobs/contact-master-list/contact-master-list.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { getFilenameFromUrl } from 'src/utils/url-helper.service';
import { ConfigService } from '@nestjs/config';




@Controller('wash-whats-app-numbers')
export class WashWhatsAppNumbersController {
  constructor(
    private readonly washWhatsAppNumbersService: WashWhatsAppNumbersService,
    private readonly whatsAppWasherApiService : WhatsAppWasherApiService,
    private readonly contactWasherApiCompletedFilesService: ContactWasherApiCompletedFilesService,
    private readonly csvStreamProcessor: CsvStreamProcessor,
    private readonly contactMasterListService: ContactMasterListService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly jobDetailsService: JobDetailsService,
    private readonly confg: ConfigService
  ) {}

  @Post(':jobId')
  async processJob(@Param('jobId') jobId: string): Promise<void> {
    const apiClsuterData = await this.whatsAppWasherApiService.getClusterStatus();
    const availableApis = apiClsuterData.filter((x) => x.available);
    const activeCount = availableApis.length;

    await this.processAllCompletedFiles(apiClsuterData);
    await this.jobDetailsService.updateJobDetailsUsingMasterList();
    
    if (activeCount == 0) {
      console.log('No available APIs to process the job.');
      return;
    }

    for (const apiInstance of availableApis) {
      try {
        const jobData = await this.jobDetailsService.getDetailsToWash(apiInstance.url, this.confg.get<number>('API_WASHING_BATCH_SIZE'), this.confg.get<number>('DATAWASH_COOLDOWN_IN_MINUTES'));
        const {fileName} = await this.whatsAppWasherApiService.sendDataForManualWashing(apiInstance, jobData);  

        await this.contactWasherApiCompletedFilesService.upsert({
          sent_to_washer: apiInstance.url,
          file_name: fileName.replace('.csv', ''),
      });

      } catch (error) {
        console.error(error);
        console.error(`Error processing with API instance ${apiInstance.id}:`, error.message);
      }
    }
    

  }

  async processAllCompletedFiles(apiClusterData) {
    for (const apiInstance of apiClusterData) {
        for (const workLogEntry of apiInstance.workLog) {
          try{
            if (workLogEntry.status.toLowerCase() === 'completed') {
                try {
                    const record = await this.contactWasherApiCompletedFilesService.findOneByUrl(workLogEntry.processedFileURL);
                    console.log(`Processing file ${workLogEntry.processedFileURL} record.name: ${record?.clean_file_url}`);
                    
                    if (!record?.clean_file_url)  {
                      console.log(`Importing file ${workLogEntry.processedFileURL}`);
                        const fileName = getFilenameFromUrl(workLogEntry.processedFileURL);
                        const gcpFilePath = 'WashedApiOutputFile/' + fileName;
                        await this.gcpBucketService.ensureFolderExists(this.confg.get<string>("GCP_BUCKET_NAME"),'WashedApiOutputFile');
                        await this.gcpBucketService.saveFileFromUrlToBucket(workLogEntry.processedFileURL, this.confg.get<string>("GCP_BUCKET_NAME"), gcpFilePath);
                        const readStream = await this.gcpBucketService.getFileStream(this.confg.get<string>("GCP_BUCKET_NAME"), gcpFilePath);

                        await this.csvStreamProcessor.import(
                          readStream,
                          (rowData, rowIndex) => {
                            // Phone Number,Sanitized Number,Status
                            const entity = {
                              phone_number: rowData['Sanitized Number'] || '',
                              update_source_id: '1',
                              whatsapp_status_date: this.whatsAppWasherApiService.extractEpochDate(fileName),
                              whatsapp_status_id: this.getStatusIdFromString(rowData['Status']),
                            };
                            // console.log(`Processing rowData ${rowIndex}: ${JSON.stringify(rowData)}`);
                            // console.log(`Processing entity ${rowIndex}: ${JSON.stringify(entity)}`);
                            return entity;
                          },
                          async (batchData) => { 
                            console.log('Saving to the database...');
                            this.contactMasterListService.upsert(batchData);
                          }, 
                          null);
                        
                        await this.contactWasherApiCompletedFilesService.upsert({
                          clean_file_url: workLogEntry.processedFileURL,
                          recieved_from_washer: true,
                          file_name: fileName.split('-')[0]
                        });
      
                        console.log(`Import Complete for ${workLogEntry.processedFileURL}`);
                    }
                }
                catch (error) {
                  console.error(error);
                  console.log(`Error processing file ${workLogEntry.processedFileURL}:`, error.message);
                }
            }
          }
          catch (error) {
            console.error(`Error processing work log entry ${workLogEntry.id}:`, error.message);
          }
        }
    }
}

private getStatusIdFromString(status: string): string {
  if (status.toLowerCase() === 'whatsapp number') {
     return '2'; // onWhatsApp
  }
  else
  {
    return '1'; // notOnWhatsApp

  }
}

}