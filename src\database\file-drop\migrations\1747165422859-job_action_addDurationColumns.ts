import { MigrationInterface, QueryRunner } from "typeorm";

export class JobActionAddDurationColumns1747165422859 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action ADD COLUMN started_at datetime`);
        await queryRunner.query(`ALTER TABLE job_action ADD COLUMN completed_at datetime`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN started_at`);
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN completed_at`);
    }

}
