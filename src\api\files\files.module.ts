import { Modu<PERSON> } from '@nestjs/common';
import { FilesController } from './files.controller';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { AuthModule } from 'src/shared/auth/auth.module';
import { JwtService } from '@nestjs/jwt';


@Module({
    imports: [DatabaseModule, JobFilesModule, AuthModule],
  controllers: [FilesController],
  providers:[GcpBucketService, JobFilesService, JwtService]
})
export class FilesModule {}