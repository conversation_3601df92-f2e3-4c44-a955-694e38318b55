import { Test, TestingModule } from '@nestjs/testing';
import { ArgusController } from './argus.controller';
import { DataSource } from 'typeorm';

describe('ArgusController', () => {
  let controller: ArgusController;
  let mockDataSource: Partial<DataSource>;

  beforeEach(async () => {
    mockDataSource = {
      isInitialized: true,
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ArgusController],
      providers: [
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    controller = module.get<ArgusController>(ArgusController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return server time and database connection status', async () => {
    const result = await controller.getStats();
    expect(result).toHaveProperty('serverTime');
    expect(result).toHaveProperty('isDbConnected', true);
  });
});