import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class BasicAuthGuard implements CanActivate {
  constructor(
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {

    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Basic ')) {
      throw new UnauthorizedException('Missing or invalid auth credentials');
    }

    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('utf-8');
    const [username, password] = credentials.split(':');

    if (password == process.env.FLEX_BOT_BUILDER_SECRET_KEY) {
      await this.globalAuth(username, password, request);
    } /* else {
      await this.basicUserAuth(username, password, request);
    }*/

    return true;
  }

  /*
  private async basicUserAuth(username: string, password: string, request): Promise<void> {
    const user = await this.validateUser(username, password);

    const { id, companyId, email: userEmail, firstName, lastName, userRoles } = user;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (request as any).user = {
      sub: id,
      companyId: companyId,
      email: userEmail,
      firstName,
      lastName,
      roles: userRoles?.map((role) => role?.roleId?.toString()),
      prepaid: user.company.prepaid,
      timezone: user.company.timezone,
    };
  }

  async validateUser(email: string, password: string): Promise<User> {
    const user = await this.userService.findOneWithRoles({ email });

    if (!user.isActive) {
      throw new UnauthorizedException(this.i18n.t('errors.user.not-active'));
    }

    if (BigInt(user.userTypeId) !== BigInt(userTypeEnum.SystemUser)) {
      throw new UnauthorizedException(this.i18n.t('errors.user.only-api-can-use-key'));
    }

    if (user?.password && (await bcrypt.compare(password, user.password))) {
      return user;
    } else {
      throw new UnauthorizedException(this.i18n.t('errors.auth.invalid-credentials'));
    }
  }
    */

  // This is currently reserved ChatInc Services that can operate accross companies and channels,
  // at the moment bot Builder we will need to revist this in the future.
  private async globalAuth(username: string, password: string, request): Promise<void> {
    if (
      !username ||
      password !== process.env.SECRET_KEY ||
      !process.env.SECRET_KEY
    ) {
      throw new UnauthorizedException('Missing or invalid auth credentials');
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (request as any).user = {
      id: null,
      companyId: null,
      channelId: null,
      email: null,
      firstName: null,
      lastName: null,
      userRoles: [],
      timezone: null,
    };
  }
}
