import { Controller, Get, HttpException, Req, UseGuards } from '@nestjs/common';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { DataSource } from 'typeorm';

@Controller('argus')
export class ArgusController {
  constructor(private readonly dataSource: DataSource) {}

  @Get('stats')
  async getStats() {
    const isDbConnected = await this.dataSource.isInitialized;

    return {
      serverTime: new Date(),
      isDbConnected,
      version: "1.0.1"
    };
  }

  @Get('secure')
  @UseGuards(CombinedAuthGuard)
  async getSecure(@Req() req: any) {
    return {
      serverTime: new Date(),
      reqUser: req.user,
      version: "1.0.1"
    };
  }

  @UseGuards(CombinedAuthGuard)
  @Get('run-migrations')
  async runMigrations(@Req() req: any): Promise<string> {
    const isVliad = (req.headers['x-admin-key'] = process.env.ADMIN_SECRET);

    if (!isVliad) {
      throw new HttpException('This is a Admin only Feature.', 401);
    }

    const result = await this.dataSource.runMigrations();

    return `Migrations executed: ${result.length}`;
  }
}