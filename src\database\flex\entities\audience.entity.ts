import {
    Column,
    DeleteDateColumn,
    UpdateDateColumn,
    CreateDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
  } from 'typeorm';
  
  
  @Index('audiences_company_id_index', ['companyId'], {})
  @Entity('audiences')
  export class Audience {
    @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
    id: string;
  
    @Column('bigint', { name: 'company_id' })
    companyId: string;
  
    @Column('bigint', { name: 'user_id' })
    userId: string;
  
    @Column('varchar', { name: 'name', length: 191 })
    name: string;
  
    @Column('varchar', { name: 'description', nullable: true, length: 500 })
    description: string | null;
   
    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;
  
    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
  
    @DeleteDateColumn({ name: 'deleted_at', nullable: true })
    deletedAt: Date | null;
  }
  