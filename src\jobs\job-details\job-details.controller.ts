import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Post, Put, Delete, Param, Body, Query } from '@nestjs/common';
import { JobDetailsService } from './job-details.service';
import { JobDetail } from '../../database/file-drop/entities/job-detail.entity';


@ApiTags('Job Details')
@Controller('job-details')
export class JobDetailsController {
  constructor(private readonly jobDetailsService: JobDetailsService
  ) {}

  @Get()
  async findAll(): Promise<JobDetail[]> {
    return this.jobDetailsService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<JobDetail> {
    return this.jobDetailsService.findOne(id);
  }

  @Post()
  async create(@Body() jobDetail: JobDetail): Promise<JobDetail> {
    return this.jobDetailsService.create(jobDetail);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() jobDetail: JobDetail): Promise<JobDetail> {
    return this.jobDetailsService.update(id, jobDetail);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.jobDetailsService.remove(id);
  }

}