import { Injectable } from '@nestjs/common';
import { JobActionsService } from 'src/database/file-drop/services/job-actions.service';
import { JobActionStatusEnum, JobActionTypeEnum, JobStatus, JobSubStatusEnum } from 'src/database/file-drop/entities/enums';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { JobAction } from 'src/database/file-drop/entities/job-action.entity';
import { ActionExecutionService } from './action-execution.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { start } from 'repl';

@Injectable()
export class ActionProcessingService {
    
    constructor(
        private readonly jobActionService: JobActionsService,
        private readonly actionExecutionService: ActionExecutionService,
        private readonly httpService: HttpService,
        private readonly config: ConfigService,
        private readonly jobService: JobService,
  ) {}


  async process(actionId: string, onRun : (action: JobAction) => Promise<void>, jobSubStatus: JobSubStatusEnum): Promise<void> {
    const action = await this.jobActionService.findOne(actionId);
    const startTime = Date.now();
    console.log(`Processing Action with action ID: ${actionId} - ${action?.action} : ${action?.job_id} `);
    
    if (!action) {
      console.error(`Action with ID ${actionId} not found`);
      throw new Error(`Action with ID ${actionId} not found`);
    }

    if (action.status_id !== JobActionStatusEnum.Processing.toString()) {
      await this.jobActionService.startAction(actionId, JobActionStatusEnum.Processing, [JobActionStatusEnum.Ready, JobActionStatusEnum.QueuedForProcessing]);
    }

    await this.jobService.update(action.job_id, { status_id: JobStatus.Processing.toString(),  job_sub_status_id: jobSubStatus.toString() });

    try{
      await onRun(action); 

      let actionsToTrigger = [];
      if (action.repeat_after_x_seconds && action.repeat_after_x_seconds > 0) {
        actionsToTrigger = await this.jobActionService.completeAction(
          action, 
          this.config.get<number>('LOCK_DURATION_IN_MINUTES'), 
          JobActionStatusEnum.Ready.toString(), 
          new Date(Date.now() + 1000 * action.repeat_after_x_seconds)
        );
      }
      else{
        actionsToTrigger = await this.jobActionService.completeAction(action, this.config.get<number>('LOCK_DURATION_IN_MINUTES'));
      }

      await this.jobService.calculateStatus(action.job_id);
    
      for (const action of actionsToTrigger)  {
        this.actionExecutionService.executeAction(
          {
            job_id: action.job_id,
            actionId: action.id,
            data: action.data,
            action: action.action,
            actionType: Number(action.job_action_type_id) as JobActionTypeEnum,
            authToken: action.job.auth_token
          }
        );
      }
    }
    catch(error)
    {
      if (action.repeat_after_x_seconds && action.repeat_after_x_seconds > 0) {
         await this.jobActionService.update(actionId, {trigger_time: new Date(Date.now() + 1000 * action.repeat_after_x_seconds)});
      }
      else {
        await this.jobActionService.setStatus(actionId, JobActionStatusEnum.Errored);
      }
      console.error(`Error running Action with action ID: ${actionId} - ${action.action} : ${action?.job_id} `);
      console.error(error);
    }
    const duration = Date.now() - startTime;
    console.log(`Done Processing Action with action ID: ${actionId} - ${action?.action} : ${action?.job_id}. Duration: ${duration} ms`);
  } 

  async setSubStatus(action: JobAction, subStatus: JobSubStatusEnum): Promise<void> {
    await this.jobService.update(action.job_id, { job_sub_status_id: subStatus.toString() });
  }
}