import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Templates } from './templates.entity';
import { TemplateGroupTemplate } from './template-group-template.entity';

@Entity('template_groups')
export class TemplateGroup {
  @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
  id: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @CreateDateColumn({ type: 'timestamp', name:"created_at", default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({name:"updated_at", type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({name:"variable_group", type: 'varchar', length: 500, nullable: true })
  variableGroup: string | null;

  @Column({name:"has_media", type: 'tinyint', width: 1, nullable: true })
  hasMedia: boolean | null;

  @Column({ type: 'bigint', unsigned: true, name:"company_id", nullable: false })
  companyId: string;

  @OneToMany(() => TemplateGroupTemplate, (tgt) => tgt.templateGroup, { cascade: true })
  templateGroupTemplates: TemplateGroupTemplate[];

  
  @ManyToMany(() => Templates, (template) => template.templateGroups)
  @JoinTable({
    name: 'template_group_templates',
    joinColumn: { name: 'template_group_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'template_id', referencedColumnName: 'id' },
  })
  templates: Templates[];
}