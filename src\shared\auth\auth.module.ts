import { Global, Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtStrategy } from '../jwt.strategy';
import { AuthService } from '../../utils/auth.service';
import { BasicAuthGuard } from './basic-auth.guard';
import { JwtAuthGuard } from './jwt-auth.guard';
import { CombinedAuthGuard } from './combined.auth.guard';

@Global() 
@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('ACCESS_TOKEN_SECRET'),
        signOptions: { expiresIn: '1h' },
      }),
    }),
  ],
  providers: [AuthService, JwtStrategy, BasicAuthGuard, JwtAuthGuard, CombinedAuthGuard],
  exports: [AuthService, JwtAuthGuard, BasicAuthGuard],
})
export class AuthModule {}