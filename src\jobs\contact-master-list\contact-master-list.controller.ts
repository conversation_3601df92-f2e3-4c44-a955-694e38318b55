import { Controller, Get, Post, Body, Patch, Param, Delete, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ContactMasterListService } from './contact-master-list.service';
import { ContactMasterList } from '../../database/file-drop/entities/contact-master-list.entity';
import { GcpBucketService } from '../../utils/gcp-bucket.service';
import { ConfigService } from '@nestjs/config';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';
import { GetSignedUrlConfig, Storage } from '@google-cloud/storage';

@Controller('contact-master-list')
export class ContactMasterListController {
  constructor(
    private readonly contactMasterListService: ContactMasterListService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService,
    private readonly csvStreamProcessor: CsvStreamProcessor,
  ) {}

  @Get()
  findAll() {
    return this.contactMasterListService.findAll();
  }

  @Get('generate-signed-url')
  async generateSignedUrl(@Body('fileName') fileName?: string) {
    const storage = new Storage();
    const bucketName = this.config.get<string>('GCP_BUCKET_NAME'); // Replace with your bucket name
    const bucket = storage.bucket(bucketName);

    if (!fileName) {
      fileName = `${Date.now()}_uploaded_file`; // Generate a file name based on the current timestamp
    }

    const file = bucket.file('contact-master-list/' + fileName);
    const options: GetSignedUrlConfig = {
      version: 'v4',
      action: 'write', // Ensure this is explicitly set to a valid action
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
    };

    const url = await file.getSignedUrl(options); // Await the promise and use the first element of the response

    return { signedUrl: url[0], fileName };
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    console.log('Finding contact with ID:', id);
    return this.contactMasterListService.findOne(id);
  }

  @Post()
  create(@Body() contact: ContactMasterList) {
    return this.contactMasterListService.create(contact);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() contact: Partial<ContactMasterList>) {
    return this.contactMasterListService.update(id, contact);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.contactMasterListService.remove(id);
  }

  @Post('process-file')
  async processFile(@Body() data: { bucket: string; fileName: string }): Promise<string> {
    const { bucket, fileName } = data;

    const readStream = await this.gcpBucketService.getFileStream(bucket, fileName);

    await this.csvStreamProcessor.import(
      readStream,
      (rowData, rowIndex) => {
        // Transform row data if needed
        return this.contactMasterListService.createEntityObject(rowData, rowIndex);
      },
      async (batchData) => {
        this.contactMasterListService.upsert(batchData);
      },
      null
    );

    return `File ${fileName} imported successfully.`;
  }
}