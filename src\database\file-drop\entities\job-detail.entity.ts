import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Job } from './job.entity';

@Entity('job_detail')
export class JobDetail {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'bigint' })
  job_id: string;

  @Column({ type: 'varchar', length: 255 })
  phone_number: string;

  @Column({ type: 'bigint' })
  whatsapp_status_id: string;

  @Column({ type: 'varchar', length: 255 })
  sanitized_number: string;

  @Column({ type: 'datetime', nullable: true })
  whatsapp_status_date: Date;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @Column({ type: 'bigint', nullable: true })
  job_detail_status_id: string;

  @Column({ type: 'json', nullable: true })
  row_data: object;

  @Column({ type: 'json', nullable: true })
  report_data: object;

  @Column({ type: 'datetime', nullable: true })
  report_data_updated_at: Date;

  @Column({ type: 'bigint' })
  contact_id: string;

  @Column({ type: 'bigint' })
  message_id: string;

  @Column({ type: 'int', nullable: true })
  source_row_index: number;

  @Column({ type: 'text', nullable: true })
  system_message: string;

  @ManyToOne(() => Job, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;
}