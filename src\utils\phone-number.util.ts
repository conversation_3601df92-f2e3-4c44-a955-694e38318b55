import * as libphonenumber from 'google-libphonenumber';

export class PhoneNumberUtil {
  private static phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();

  static validateAndSanitize(number: string, countryCode: string): { sanitizedNumber: string | null; isValid: boolean } {
    try {
      const parsedNumber = this.phoneUtil.parseAndKeepRawInput(number, countryCode);
      const isValid = this.phoneUtil.isValidNumber(parsedNumber);
      const sanitizedNumber = isValid ? this.phoneUtil.format(parsedNumber, libphonenumber.PhoneNumberFormat.E164) : null;

      if (!isValid){
        console.info(`Invalid phone number: ${number} countryCode:${countryCode} `);
      }

      return { sanitizedNumber, isValid };
    } catch (error) {
      console.error(`Error parsing phone number:${number} countryCode:${countryCode} `, error);
      return { sanitizedNumber: null, isValid: false };
    }
  }
}