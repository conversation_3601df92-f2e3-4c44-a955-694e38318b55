import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Templates } from '../entities/templates.entity';


@Injectable()
export class TemplateService {

  private readonly templateRepository: Repository<Templates>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.templateRepository = this.flexDataSource.getRepository(Templates);
  }

  async findOne(id: string, companyId: string): Promise<Templates | null> {
    return this.templateRepository.findOne({ where: { id } });
  }

  async findOneByName(name: string, channelId: string): Promise<Templates | null> {
    return this.templateRepository.findOne({ where: { name, channelId } });
  }
}