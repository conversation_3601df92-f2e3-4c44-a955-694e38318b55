import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { JobDetail } from '../../database/file-drop/entities/job-detail.entity';
import { JobDetailStatus, WhatsAppStatus } from 'src/database/file-drop/entities/enums';



@Injectable()
export class JobDetailsService {

  constructor(
    @InjectRepository(JobDetail)
    private readonly jobDetailRepository: Repository<JobDetail>,
    private dataSource: DataSource
  ) {}

  async findAll(): Promise<JobDetail[]> {
    return this.jobDetailRepository.find();
  }

  async getDetailsToWash(url: string , batchSize: number, cooldownInMinutes: number): Promise<{sanitized_number}> {
    const result = await this.dataSource.query(
      'CALL job_details_assign_to_washerWasher(?, ?, ?)', 
      [url, batchSize, cooldownInMinutes]
    );
    
    return result[0];
  }
    

  async findOne(id: string): Promise<JobDetail> {
    return this.jobDetailRepository.findOne({ where: { id } });
  }

  async create(jobDetail: JobDetail): Promise<JobDetail> {
    return this.jobDetailRepository.save(jobDetail);
  }

  async update(id: string, jobDetail: Partial<JobDetail>): Promise<JobDetail> {
    await this.jobDetailRepository.update(Number(id), jobDetail);
    return this.findOne(id);
  }

  async updateBatch(batchToProcess: JobDetail[]) : Promise<JobDetail[]> {
    return await this.jobDetailRepository.save(batchToProcess);
  }

  async updateBatchByMessageId(batchToProcess: Partial<JobDetail>): Promise<void> {
    console.log(`Updating batch by message ID: ${batchToProcess.message_id}`);

      await this.jobDetailRepository.update({ message_id : batchToProcess.message_id} , batchToProcess);
  }

  async remove(id: string): Promise<void> {
    await this.jobDetailRepository.delete(Number(id));
  }

  async upsertJobDetails(jobDetails: JobDetail[]): Promise<void> {
    const batchSize = 100;
    for (let i = 0; i < jobDetails.length; i += batchSize) {
      const batch = jobDetails.slice(i, i + batchSize);
      await this.jobDetailRepository.upsert(jobDetails,['job_id', 'source_row_index']);
    }
  }

  async removeAllForJob(jobId: string): Promise<void> {
    await this.jobDetailRepository.update({ job_id: jobId }, { deleted_at: new Date() });
  }

  async findAllByJobIdBatched(jobId, batchSize, lastRowIndex, jobDetailStatusIds: string[], whatAppStatusIds: string[]): Promise<JobDetail[]> {
    const query = this.jobDetailRepository.createQueryBuilder('jobDetail')
      .leftJoinAndSelect('jobDetail.job', 'job') // Load the 'job' relation
      .where('jobDetail.job_id = :jobId', { jobId, lastRowIndex })
      .orderBy('jobDetail.source_row_index', 'ASC')
      .take(batchSize);

    if (lastRowIndex) {
      query.andWhere('jobDetail.source_row_index > :lastRowIndex', { lastRowIndex });
    }

    if (jobDetailStatusIds && jobDetailStatusIds.length > 0) {
      query.andWhere('jobDetail.job_detail_status_id in (:...jobDetailStatusIds)', { jobDetailStatusIds });
    }

    if (whatAppStatusIds && whatAppStatusIds.length > 0) {
      query.andWhere('jobDetail.whatsapp_status_id in (:...whatAppStatusIds)', { whatAppStatusIds });
    }

    return query.getMany();
  }

  
  async findAllWithoutContactIdByJobIdBatched(jobId, batchSize, lastRowIndex): Promise<JobDetail[]> {
    const query = this.jobDetailRepository.createQueryBuilder('jobDetail')
      .where('jobDetail.job_id = :jobId', { jobId, lastRowIndex })
      .andWhere(`jobDetail.contact_id IS NULL`)
      .orderBy('jobDetail.source_row_index', 'ASC')
      .take(batchSize);

    if (lastRowIndex) {
      query.andWhere('jobDetail.source_row_index > :lastRowIndex', { lastRowIndex });
    }

    return query.getMany();
  }

  async findAllIncompleteByJobIdBatched(jobId, batchSize, lastRowIndex): Promise<JobDetail[]> {
    const query = this.jobDetailRepository.createQueryBuilder('jobDetail')
      .where('jobDetail.job_id = :jobId', { jobId, lastRowIndex })
      .andWhere(`jobDetail.job_detail_status_id <> ${JobDetailStatus.Successful}`)
      .orderBy('jobDetail.source_row_index', 'ASC')
      .take(batchSize);

    if (lastRowIndex) {
      query.andWhere('jobDetail.source_row_index > :lastRowIndex', { lastRowIndex });
    }

    return query.getMany();
  }

  async markDuplicates(jobId: string) {
    await this.dataSource.query('CALL job_details_mark_duplicate(?)', [BigInt(jobId)]);
  }

  async updateJobDetailsUsingMasterList() {
    await this.dataSource.query(
      'CALL job_details_update_using_master_list(?, ?, ?)', 
      [JobDetailStatus.SentToApi, JobDetailStatus.WashedViaApi, JobDetailStatus.SentToApi]
    );
    await this.dataSource.query(
      'CALL job_details_update_using_master_list(?, ?, ?)', 
      [JobDetailStatus.Inserted, JobDetailStatus.WashedViaDB, JobDetailStatus.ManualWashRequired]
    );
 }

}