import { ApiTags, ApiOperation, ApiOkResponse } from '@nestjs/swagger';
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Patch,
} from '@nestjs/common';
import { JobActionsService } from '../../database/file-drop/services/job-actions.service';
import { JobAction } from '../../database/file-drop/entities/job-action.entity';
import { UpdateJobActionsStatusDto } from './dtos/status-change.dto';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { plainToInstance } from 'class-transformer';

@ApiTags('Job Actions')
@UseGuards(CombinedAuthGuard)
@Controller('job-actions')
export class JobActionsController {
  constructor(private readonly jobActionsService: JobActionsService) {}

  @Get()
  findAll(): Promise<JobAction[]> {
    return this.jobActionsService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<JobAction | null> {
    return this.jobActionsService.findOne(id);
  }

  @Post()
  create(@Body() jobAction: JobAction): Promise<JobAction> {
    return this.jobActionsService.create(jobAction);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() jobAction: Partial<JobAction>): Promise<JobAction> {
    await this.jobActionsService.update(id, jobAction);
    return this.jobActionsService.findOne(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    await this.jobActionsService.remove(id);
  }

  @Patch(':jobId/action/:actionId/update-status')
  @ApiOkResponse({
    description: 'Updates the status of a job action',
    type: UpdateJobActionsStatusDto,
  })
  @ApiOperation({ summary: 'Update all job actions for a specific job' })
  async updateJobActionsStatus(
    @Param('jobId') jobId: string,
    @Param('actionId') actionId: string,
    @Body() updateDto: UpdateJobActionsStatusDto,
  ): Promise<UpdateJobActionsStatusDto> {
    const jobAction = await this.jobActionsService.setStatusForJobActions(
      jobId,
      actionId,
      updateDto,
    );

    return plainToInstance(UpdateJobActionsStatusDto, jobAction);
  }
}
