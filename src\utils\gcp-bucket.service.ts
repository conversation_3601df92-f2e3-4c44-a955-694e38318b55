import { Storage } from '@google-cloud/storage';
import { Injectable } from '@nestjs/common';
import { CreateReadStreamOptions } from 'fs/promises';
import { Readable, ReadableOptions, Writable } from 'stream';
import axios from 'axios';

@Injectable()
export class GcpBucketService {
  private storage: Storage;

  constructor() {
    this.storage = new Storage();
  }

  async saveFileFromUrlToBucket(fileUrl: string, bucketName: string, fileName: string): Promise<void> {
    const fileStream = await this.getFileStreamFromExternalUrl(fileUrl);
    const writableStream = await this.getWritableStream(bucketName, fileName);

    /*
    console.log(`setting expiary`);
    // Set a TTL (Time-To-Live) for the file before it is deleted
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);
    await file.save('');

    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 7); // Set TTL to 7 days from now

    await file.setMetadata({
      temporaryHold: false,
      retentionExpirationTime: expirationDate.toISOString(),
    });
    */

    console.log(`Returning stream`);

    return new Promise((resolve, reject) => {
      fileStream
        .pipe(writableStream)
        .on('finish', resolve)
        .on('error', reject);
    });
  }

  async getFileStreamFromExternalUrl(fileUrl: string): Promise<Readable> {
    const response = await axios({
      url: fileUrl,
      method: 'GET',
      responseType: 'stream',
    });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch file from URL: ${fileUrl}. Status code: ${response.status}`);
    }

    return response.data as Readable;
  }

  async getFileStreamFromUrl(fileUrl: string): Promise<Readable> {
    const url = new URL(fileUrl);
    const bucketName = url.hostname.split('.')[0]; // Extract bucket name from URL
    const fileName = decodeURIComponent(url.pathname.slice(1)); // Extract file name from URL

    return this.getFileStream(bucketName, fileName);
  }

  async getFileStream(bucketName: string, fileName: string): Promise<Readable> {
    console.log(`Creating read stream for file: ${fileName} in bucket: ${bucketName}`);
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    const [exists] = await file.exists();
    if (!exists) {
      throw new Error(`File ${fileName} does not exist in bucket ${bucketName}`);
    }

    return file.createReadStream({ highWaterMark: 1024 } as unknown as CreateReadStreamOptions) as Readable;
  }

  async getWritableStream(bucketName: string, fileName: string, options?: ReadableOptions): Promise<Writable> {
      console.log(`Creating writable stream for file: ${fileName} in bucket: ${bucketName}`);
    
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      const writableStream = file.createWriteStream({
        resumable: false,
        gzip: true,
        ...options,
      });

      return writableStream;
    }

    async deleteFile(bucketName: string, fileName: string): Promise<void> {
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      await file.delete();
    }

    async uploadFile(buffer: Buffer, bucketName: string, fileName: string): Promise<void> {
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      const stream = file.createWriteStream({
        resumable: false,
        gzip: true,
      });

      return new Promise((resolve, reject) => {
        stream
          .on('finish', resolve)
          .on('error', reject)
          .end(buffer);
      });
    }

    async ensureFolderExists(bucketName: string, folderName: string): Promise<void> {
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(`${folderName}/.placeholder`);
    
      const [exists] = await file.exists();
      if (!exists) {
        await file.save(''); // Create a placeholder file to ensure the folder exists
      }
    }
}