import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm';

@Entity('job_stats')
export class JobStats {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'datetime', nullable: true })
  last_updated: Date;

  @Column({ type: 'int', nullable: true })
  total_details: number;

  @Column({ type: 'int', nullable: true })
  total_complete: number;

  @Column({ type: 'int', nullable: true })
  total_unprocessed: number;

  @Column({ type: 'int', nullable: true })
  total_manualwashrequired: number;

  @Column({ type: 'int', nullable: true })
  total_apiwashrequired: number;

  @Column({ type: 'int', nullable: true })
  total_senttoapi: number;

  @Column({ type: 'int', nullable: true })
  total_washedviadb: number;

  @Column({ type: 'int', nullable: true })
  total_washedviaapi: number;

  @Column({ type: 'int', nullable: true })
  total_duplicate: number;

  @Column({ type: 'int', nullable: true })
  total_invalidnumber: number;

  @Column({ type: 'int', nullable: true })
  total_paused: number;

  @Column({ type: 'int', nullable: true })
  total_stopped: number;

  @Column({ type: 'int', nullable: true })
  total_preprocessingskippedduetocutofftime: number;

  @Column({ type: 'bigint', nullable: true })
  job_id: string;

  @Column({ type: 'double', default: 0 })
  progress: number;

  @Column({ type: 'json', nullable: true })
  created_by: any;

  @Column({ type: 'json', nullable: true })
  audience: any;

  @Column({ type: 'varchar', length: 255 })
  display_status: string;

  @Column({ type: 'varchar', length: 5000 })
  failed_file_url: string;

  @Column({ type: 'varchar', length: 5000 })  
  success_file_url: string;

  @Column({ type: 'varchar', length: 500 })
  failed_file_name: string;

  @Column({ type: 'varchar', length: 500 })  
  success_file_name: string;
  
}