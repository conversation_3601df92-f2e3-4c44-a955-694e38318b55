import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { EngineModule } from 'src/engine/engine.module';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';

import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { UtilModule } from 'src/utils/util.module';
import { FlexCreateCampaignController } from './flex-create-campaign.controller';
import { JobStatsService } from 'src/database/file-drop/services/job-stats.service';

@Module({
      imports: [DatabaseModule, EngineModule, FlexDatabaseModule, UtilModule],
  controllers: [FlexCreateCampaignController],
  providers: [JobDetailsService, JobStatsService],
})
export class FlexCreateCampaignModule {}