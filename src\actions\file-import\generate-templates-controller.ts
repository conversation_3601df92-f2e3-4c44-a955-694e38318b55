import { Controller, Get, Query, Req, Res, UseGuards } from "@nestjs/common";
import { isInt } from "class-validator";
import { Response } from "express";
import { TemplateGroupsService } from "src/database/flex/services/template-groups.service";
import { JwtAuthGuard } from "src/shared/auth/jwt-auth.guard";
import { FileNameService } from "./file-name.service";


@Controller('file-import/generate-templates')
export class GenerateTemplatesController {
  constructor(
    private readonly templateGroupsService: TemplateGroupsService,
    private readonly fileNameService: FileNameService,
  ) {}
  
  @Get('contact-import')
  generateContactImportTemplate(@Res() res: Response): void {
    const csvHeader = 'mobile';
    const csvBody = '27831234567';

    this.sendCsvFile(res, csvHeader, csvBody, 'contact_template.csv');
  }

  @UseGuards(JwtAuthGuard)
  @Get('campaign-import')
  async generateCampaignImportTemplate(@Req() req, @Res() res: Response, @Query('templateGroupId',) templateGroupId: string,
  @Query('sendDateAsEpoch') sendDateAsEpoch: string = 'ScheduleForEpoch',
  @Query('wash') washData?: boolean, 
  @Query('fileName') fileName?: string, 
  @Query('importIntoFlex') importIntoFlex?: boolean,
  @Query('cutOffMinutes') cutOffMinutes?: string,
  @Query('channelId') channelId?: string,): Promise<void> {
    const templateGroup = await this.templateGroupsService.findOne(templateGroupId, req.user.companyId);
    const firstTemplate = templateGroup?.templates[0]?.components;
    const components : any[] = firstTemplate ? JSON.parse(firstTemplate) : [];
    const bodyComponent = components.find(c => c.type?.toUpperCase() === "BODY");
    const examples = bodyComponent?.example as string[] || [];

    if (!templateGroup) {
      res.status(400).send('Template group not found');
      return;
    }

    if (!channelId) {
      res.status(400).send('channelId is required for campaign template');
      return;
    }


    let csvHeader = 'mobile';
    let csvBody = '27831234567';
    let templateName = this.fileNameService.generagenerateCampaignFileNameteFileName(null, importIntoFlex, washData, sendDateAsEpoch, templateGroup.name, cutOffMinutes, channelId, '');
    
    // 'campaign_template';

    if (templateGroup.hasMedia)
    {
      csvHeader += ',mediaUrl';
      csvBody += ',https://example.com/media.jpg';
    }
    

    console.log(JSON.stringify(examples));

    templateGroup.variableGroup.split(',').forEach((variable) => {
      const exampleIndex = /^\d+$/.test(variable) ? parseInt(variable) - 1 : null;

      csvHeader += `,${variable}`;
      csvBody +=  `,` + (exampleIndex != null && examples[exampleIndex] ? examples[exampleIndex] : variable);
    });
    
    this.sendCsvFile(res, csvHeader, csvBody, templateName);
  }

  private sendCsvFile(res: Response<any, Record<string, any>>, csvHeader: string, csvBody: string, templateName: string) {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${templateName}"`);
    res.send(`${csvHeader}\n${csvBody}`);
  }
}