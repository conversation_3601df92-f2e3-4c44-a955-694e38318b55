import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { WashWhatsAppNumbersService } from './wash-whats-app-numbers.service';
import { WashWhatsAppNumbersController } from './wash-whats-app-numbers.controller';
import { JobDetailsService } from '../../jobs/job-details/job-details.service';
import { ContactWasherApiCompletedFilesService } from '../../jobs/contact-washer-api-completed-files/contact-washer-api-completed-files.service';
import { ContactMasterListService } from '../../jobs/contact-master-list/contact-master-list.service';
import { JobDetail } from '../../database/file-drop/entities/job-detail.entity';
import { ContactWasherApiCompletedFiles } from '../../database/file-drop/entities/contact-washer-api-completed-files.entity';
import { ContactMasterList } from '../../database/file-drop/entities/contact-master-list.entity';
import { WhatsAppWasherApiService } from '../../utils/whats-app-washer-api';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';

@Module({
  imports: [TypeOrmModule.forFeature([JobDetail, ContactWasherApiCompletedFiles, ContactMasterList]), ConfigModule],
  controllers: [WashWhatsAppNumbersController],
  providers: [WashWhatsAppNumbersService, JobDetailsService, ContactWasherApiCompletedFilesService, ContactMasterListService, WhatsAppWasherApiService, CsvStreamProcessor, GcpBucketService],
})
export class WashWhatsAppNumbersModule {}