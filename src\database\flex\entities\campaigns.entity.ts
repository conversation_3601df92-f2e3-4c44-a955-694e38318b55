import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { User } from './user.entity';

@Entity('campaigns')
export class Campaign {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'channel_id', unsigned: true })
  channelId: string;

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: string;

  @Column('longtext', { name: 'segment_recipient', nullable: true })
  segmentRecipient: string | null;

  @Column('longtext', { name: 'individual_recipient', nullable: true })
  individualRecipient: string | null;

  @Column('int', { name: 'state_id' })
  stateId: number;

  @Column('varchar', { name: 'variables', nullable: true, length: 1000 })
  variables: string | null;

  @Column('text', { name: 'text', nullable: true })
  text: string | null;

  @Column('varchar', { name: 'media_type', nullable: true, length: 191 })
  mediaType: string | null;

  @Column('varchar', { name: 'media_url', nullable: true, length: 191 })
  mediaUrl: string | null;

  @Column('varchar', { name: 'short_url', nullable: true, length: 255 })
  shortUrl: string | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('text', { name: 'campaign_name' })
  name: string;

  @Column('text', { name: 'campaign_duration' })
  campaignDuration: string;

  @Column('bigint', { name: 'campaign_status_id', unsigned: true })
  campaignStatusId: string;

  @Column('bigint', { name: 'message_type_id', unsigned: true, nullable: true })
  messageTypeId: string | null;

  @Column('tinyint', { name: 'reverse_billing', width: 1, default: () => 0 })
  reverseBilling: boolean;

  @Column('bigint', { name: 'campaign_type_id', unsigned: true, nullable: true })
  campaignTypeId: string;

  @Column('datetime', { name: 'sent_at', nullable: true })
  sentAt: Date | null;

  @Column('tinyint', { name: 'payment_link', width: 1, default: () => 0 })
  paymentLink: boolean;

  @Column('varchar', { name: 'result', nullable: true, length: 191 })
  result: string | null;

  @Column('tinyint', { name: 'sftp_campaign', width: 1, default: () => 0 })
  sftpCampaign: boolean;

  @Column('varchar', { name: 'prepaid_template_type', nullable: true, length: 191 })
  prepaidTemplateType: string | null;
  
  @Column('bigint', { name: 'channel_type', unsigned: true })
  channelType: string;

  @Column('bigint', { name: 'template_id', unsigned: true })
  templateId: string;
  
  // Relations with other tables
  /*
  @ManyToOne(() => CampaignStatus, (campaignStatus) => campaignStatus.campaigns)
  @JoinColumn([{ name: 'campaign_status_id', referencedColumnName: 'id' }])
  campaignStatus: CampaignStatus;

  @ManyToOne(() => CampaignTypes, (campaignTypes) => campaignTypes.campaigns)
  @JoinColumn([{ name: 'campaign_type_id', referencedColumnName: 'id' }])
  campaignType: CampaignTypes;

  @JoinColumn([{ name: 'channel_id', referencedColumnName: 'id' }])
  @ManyToOne(() => Channels, (channels) => channels.campaigns)
  channel: Channels;

  @ManyToOne(() => ChannelTypes, (channelType) => channelType.channels)
  @JoinColumn([{ name: 'channel_type', referencedColumnName: 'id' }])
  channelType: ChannelTypes;

  @ManyToOne(() => Templates, (templates) => templates.campaigns)
  @JoinColumn([{ name: 'template_id', referencedColumnName: 'id' }])
  template: Templates;

  @ManyToOne(() => MessageType, (messageTypes) => messageTypes.campaigns)
  @JoinColumn([{ name: 'message_type_id', referencedColumnName: 'id' }])
  messageType: MessageType;

  @ManyToOne(() => User)
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: User;


  @ManyToOne(() => CampaignTrackingStats, (stats) => stats.campaign, {
    cascade: true, // Optional based on your requirement
  })
  @JoinColumn([{ name: 'id', referencedColumnName: 'campaign_id' }])
  campaignTrackingStats: CampaignTrackingStats;

  @OneToMany(() => CampaignAudience, (campaignAudience) => campaignAudience.campaign)
  campaignAudiences: CampaignAudience[];

  @ManyToOne(() => Company)
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Company;
  */
  @Column('text', { name: 'carousel', nullable: true })
  carousel: string | null;

  @Column('datetime', { name: 'scheduled_for', nullable: true })
  scheduledFor: Date | null;

  @Column({ name: 'company_id' })
  companyId: string;


}
