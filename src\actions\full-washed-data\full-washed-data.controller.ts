import { Controller, Post, Body, Param, Get } from '@nestjs/common';
import { FullWashedDataService } from './full-washed-data.service';
import { ApiTags, ApiBody } from '@nestjs/swagger';
import { debug } from 'console';

@ApiTags('Full Washed Data')
@Controller('actions/full-washed-data')
export class FullWashedDataController {
  constructor(private readonly fullWashedDataService: FullWashedDataService) {}

  @Get('export/:jobId')
  @ApiBody({ description: 'Export full washed data to GCP bucket', type: Object })
  async exportFullWashedData_get(@Param('jobId') jobId: string): Promise<string> {
    console.log(`Creating Full Washed Data Export File for Job ID: ${jobId}`);
   return this.exportFullWashedData_post({ jobId });
  }

  @Post('export')
  @ApiBody({ description: 'Export full washed data to GCP bucket', type: Object })
  async exportFullWashedData_post(@Body() exportDetails: { jobId: string;}): Promise<string> {
    await this.fullWashedDataService.createFullWashedDataExportFile(exportDetails.jobId);
    return `Data exported`;
  }
}