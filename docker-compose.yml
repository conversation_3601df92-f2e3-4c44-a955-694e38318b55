version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      DB_HOST: db
      DB_PORT: 3306
      DB_USERNAME: root
      DB_PASSWORD: password
      DB_NAME: file_drop_jobs
    depends_on:
      - db

  db:
    image: mysql:8
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: file_drop_jobs
    ports:
      - '3306:3306'