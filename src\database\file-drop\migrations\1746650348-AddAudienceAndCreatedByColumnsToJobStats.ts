import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAudienceAndCreatedByColumnsToJobStats1680000000001 implements MigrationInterface {
  name = 'AddAudienceAndCreatedByColumnsToJobStats1680000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE job_stats ADD COLUMN created_by JSON`);
    await queryRunner.query(`ALTER TABLE job_stats ADD COLUMN audience JSON`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE job_stats DROP COLUMN created_by`);
    await queryRunner.query(`ALTER TABLE job_stats DROP COLUMN audience`);
  }
}