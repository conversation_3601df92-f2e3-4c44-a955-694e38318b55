import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewJobActionTypes1746981464970 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            INSERT INTO job_action_type (id, name)
            VALUES 
            (6, 'Export Wash Wahats App Numbers Data'),
            (7, 'Create Flex Campaign'),
            (8, 'Update Campaign Results'),
            (9, 'Export Campaign Results'),
            (10, 'Upload Capaign Results');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DELETE FROM job_action_type
            WHERE id IN (6, 7, 8, 9, 10);
        `);
    }

}
