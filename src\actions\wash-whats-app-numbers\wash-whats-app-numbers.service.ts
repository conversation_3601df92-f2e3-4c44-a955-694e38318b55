import { Injectable } from '@nestjs/common';
import { JobDetailsService } from '../../jobs/job-details/job-details.service';
import { ContactWasherApiCompletedFilesService } from '../../jobs/contact-washer-api-completed-files/contact-washer-api-completed-files.service';
import { ContactMasterListService } from 'src/jobs/contact-master-list/contact-master-list.service';

@Injectable()
export class WashWhatsAppNumbersService {
  constructor(
    private readonly jobDetailsService: JobDetailsService,
    private readonly contactWasherApiCompletedFilesService: ContactWasherApiCompletedFilesService,
    private readonly contactMasterListService : ContactMasterListService,
  ) {}

  async processJob(jobId: string): Promise<void> {
    await this.jobDetailsService.updateJobDetailsUsingMasterList();



  }
}