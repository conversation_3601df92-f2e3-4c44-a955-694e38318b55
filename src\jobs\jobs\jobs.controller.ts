import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Req } from '@nestjs/common';
import { JobService } from './jobs.service';
import { Job } from '../../database/file-drop/entities/job.entity';
import { JwtAuthGuard } from 'src/shared/auth/jwt-auth.guard';
import { ListResponseDto } from 'src/shared/dtos/list-resposne.dto';
import { JobAction } from 'src/database/file-drop/entities/job-action.entity';

@UseGuards(JwtAuthGuard)
@Controller('jobs')
export class JobController {
  constructor(private readonly jobService: JobService,
  ) {}

  @Get()
  async findAll(@Req() req): Promise<ListResponseDto<Job>> {
    const companyId = req.user.companyId; 

    const jobs = await this.jobService.findAll(companyId);



    return new ListResponseDto<Job>(
      10,
      1,
      1,
      jobs
    );
  }

  @Get(':jobId/actions')
  async findAllActions(@Req() req, @Param('jobId') jobId: string): Promise<ListResponseDto<JobAction>> {
    const companyId = req.user.companyId; 

    const jobs = await this.jobService.findOne(jobId,companyId, ['actions']);

    return new ListResponseDto<JobAction>(
      jobs.actions.length,
      1,
      jobs.actions.length,
      jobs.actions
    );
  }


  @Get(':id')
  async findOne(@Req() req ,@Param('id') id: string): Promise<Job | null> {
    const companyId = req.user.companyId; 
    const userId = req.user.userId; 

    return this.jobService.findOne(id, companyId, null);
  }

  @Post()
  async create(@Body() job: Partial<Job>): Promise<Job> {
    return this.jobService.create(job);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() job: Partial<Job>): Promise<Job> {
    return this.jobService.update(id, job);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.jobService.remove(id);
  }
}