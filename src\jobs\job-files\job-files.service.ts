import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';
import { FileTypeEnum } from 'src/database/file-drop/entities/enums';

@Injectable()
export class JobFilesService {
  azureBlobService: any;
  constructor(
    @InjectRepository(JobFiles)
    private readonly jobFilesRepository: Repository<JobFiles>,
  ) {}

  async findAll(): Promise<JobFiles[]> {
    return this.jobFilesRepository.find();
  }

  async findOrigonalFileForJob(jobId: string): Promise<JobFiles> {
    return this.jobFilesRepository.findOne({
      where: {
        job_id: jobId,
        file_type_id: FileTypeEnum.OrigonalFile.toString(),
      },
    });
  }

  async findOne(id: string): Promise<JobFiles | null> {
    const jobFile = this.jobFilesRepository.findOne({ where: { id } });

    if (!jobFile) {
      throw new HttpException('Job file not found', HttpStatus.NOT_FOUND);
    }

    return jobFile;
  }

  async create(jobFile: Partial<JobFiles>): Promise<JobFiles> {
    return this.jobFilesRepository.save(jobFile);
  }

  async update(id: string, jobFile: Partial<JobFiles>): Promise<void> {
    await this.jobFilesRepository.update(id, jobFile);
  }

  async remove(id: string): Promise<void> {
    await this.jobFilesRepository.delete(id);
  }

  async deleteAllFilesForJob(jobId: string): Promise<void> {
    await this.jobFilesRepository.update(
      { job_id: jobId },
      { deleted_at: new Date().toISOString() },
    );
  }

  async getFilesForJob(jobId: string): Promise<JobFiles[]> {
    return this.jobFilesRepository.find({ where: { job_id: jobId } });
  }

}
