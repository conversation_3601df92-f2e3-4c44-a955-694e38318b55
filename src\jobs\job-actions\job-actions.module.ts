import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobAction } from '../../database/file-drop/entities/job-action.entity';
import { JobActionsController } from './job-actions.controller';
import { JobActionsService } from '../../database/file-drop/services/job-actions.service';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';

@Module({
  imports: [DatabaseModule],
  controllers: [JobActionsController],
  providers: [JobActionsService],
})
export class JobActionsModule {}