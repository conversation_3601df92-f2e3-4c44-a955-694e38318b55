import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertWashAndImportFlexContact1680000000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO job_type (id, name) 
      VALUES (8, 'Wash and import Flex Contact');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM job_type WHERE id = 8;
    `);
  }
}