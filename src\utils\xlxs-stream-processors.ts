import * as xlsx from 'xlsx';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';

@Injectable()
export class XlsxStreamProcessor {
  constructor() {}

  private cleaningGC = false;

  async import(
    stream: Readable,
    onCreateRowObject: (rowData: any, rowIndex: number) => any,
    onBatchProcess: (batchData: any[]) => any,
    onHeaders: (headers: string[]) => void,
  ): Promise<void> {
    let rowIndex = 1;
    const batch: any[] = [];

    const interval = setInterval(() => {
      this.manageMemory(stream);
    }, 500);

    console.log('Streaming XLSX file.');

    return new Promise((resolve, reject) => {
      const buffers: Buffer[] = [];

      stream.on('data', (chunk) => {
        buffers.push(chunk);
      });

      stream.on('end', async () => {
        try {
          const buffer = Buffer.concat(buffers);
          const workbook = xlsx.read(buffer, { type: 'buffer' });
          const sheetName = workbook.SheetNames[0];
          const sheet = workbook.Sheets[sheetName];
          const rows = xlsx.utils.sheet_to_json(sheet, { header: 1 });

          console.log('XLSX Headers:', rows[0]);
          if (onHeaders) {
            onHeaders(rows[0] as string[]);
          } else {
            console.log('No headers function provided.');
          }

          for (let i = 1; i < rows.length; i++) {
            this.pauseStreamIfHighMemory(stream);
            let row = rows[i];
            let index = rowIndex++;

            if (index % 100 === 0) {
              console.log(`Processed ${index} rows...`);
            }

            batch.push(onCreateRowObject(row, index));

            if (batch.length >= 100) {
              console.log(`Saving to DB: ${batch.length}`);
  
              const batchToSaveToDb = batch.splice(0, 100);
              await onBatchProcess(batchToSaveToDb);
            }
          }

          if (batch.length > 0) {
            console.log('Processing remaining batch...');
            await onBatchProcess(batch);
          }

          clearInterval(interval);
          resolve(); // Ensure the promise resolves after processing is complete
        } catch (error) {
          console.error('Error processing XLSX file:', error);
          reject(error);
        } finally {
          clearInterval(interval);
        }
      });

      stream.on('error', (error) => {
        clearInterval(interval);
        console.error(error.message);
        reject(error);
      });
    });

  }

  private pauseStreamIfHighMemory(stream: Readable) {
    if (process.memoryUsage().heapUsed > 400 * 1024 * 1024) {
      console.log(`Memory usage(${process.memoryUsage().heapUsed}) too high, pausing stream...`);
      stream.pause();
    }
  }

  private manageMemory(stream: Readable) {
    console.log(`Memory usage: ${process.memoryUsage().heapUsed}, stream paused: ${stream.isPaused()}`);

    if (stream.isPaused()) {
      if (process.memoryUsage().heapUsed < 200 * 1024 * 1024) {
        stream.resume();
        console.log('Resuming stream processing...');
      } else {
        if (!this.cleaningGC) {
          console.log('Memory usage exceeded 200MB. Triggering garbage collection...');
          this.cleaningGC = true;
          if (global.gc) {
            global.gc();
          } else {
            console.warn('Garbage collection is not exposed. Consider running the process with --expose-gc.');
          }
          console.log('Garbage collection Complete.');
          this.cleaningGC = false;
        } else {
          console.log('Garbage collection running.');
        }
      }
    }
  }
}