import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Audience } from 'src/database/flex/entities/audience.entity';

@Injectable()
export class AudienceService {

  private readonly audienceRepository: Repository<Audience>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.audienceRepository = this.flexDataSource.getRepository(Audience);
  }

  async findOne(id: string, companyId: string): Promise<Audience | null> {
    return this.audienceRepository.findOne({ where: { id, companyId } });
  }
}