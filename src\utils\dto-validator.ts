import { HttpException, HttpStatus } from "@nestjs/common";
import { validate } from "class-validator";
import { plainToInstance } from 'class-transformer';

export class DtoValidator {
    static async validate<T>(dto: any, type: new () => T) {
        const instance = plainToInstance(type, dto);
        const errors = await validate(instance as object, { skipMissingProperties: false });
        const errorMessages = errors.map(err => Object.values(err.constraints || {}).join(', '));

        if (errorMessages.length > 0) {
            throw new HttpException(
                { message: 'Validation failed', errors: errorMessages },
                HttpStatus.BAD_REQUEST,
            );
        }
    }
}