import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Post, Put, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { JobDetailsFileImportService } from './job-details-file-import.service';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { JobSubStatusEnum } from 'src/database/file-drop/entities/enums';
import { JobStatsService } from 'src/database/file-drop/services/job-stats.service';

@ApiTags('Job Details')
@UseGuards(CombinedAuthGuard)
@Controller('actions/:actionId/job-details')
export class ImportJobDetailsController {
  constructor(
    private readonly jobDetailsFileImportService: JobDetailsFileImportService,
    private readonly actionProcessingService: ActionProcessingService,    
    private readonly jobsStatsService: JobStatsService,
  ) {}

  @Post('import-file/:fileId')
  async process_post(@Param('actionId') actionId: string, @Param('fileId') fileId: string, @Query('companyId') companyId: string): Promise<string> {
    console.log(`Processing action_detial file with ID: ${fileId} for company ID: ${companyId}`);
    let result = "";

    await this.actionProcessingService.process(actionId, async (action) => {
      try{
      result = await this.jobDetailsFileImportService.processFile(action.job.company_id,fileId);
      await this.jobsStatsService.updateJobStats(action.job.id);
      }
      catch (error) {
        if (error.message.startsWith("Mobile Number Header not found in file"))
        {
          console.log(error.message);
          await this.actionProcessingService.setSubStatus(action, JobSubStatusEnum['Mobile Number Header not found in file']);
        }
        
        throw error;
      }
    }, JobSubStatusEnum['Loading Data']); 

    return result;
  }

}