import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobFilesController } from './job-files.controller';
import { JobFilesService } from './job-files.service';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { GcpBucketService } from '../../utils/gcp-bucket.service';

@Module({
  imports: [DatabaseModule],
  controllers: [JobFilesController],
  providers: [JobFilesService, GcpBucketService],
  exports: [JobFilesService],
})
export class JobFilesModule {}