import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobFilesController } from './job-files.controller';
import { JobFilesService } from './job-files.service';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';

@Module({
  imports: [DatabaseModule],
  controllers: [JobFilesController],
  providers: [JobFilesService],
  exports: [JobFilesService],
})
export class JobFilesModule {}