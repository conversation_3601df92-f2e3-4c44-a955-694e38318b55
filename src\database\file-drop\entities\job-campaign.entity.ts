import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Job } from './job.entity';

@Entity('job_campaign')
export class JobCampaign {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: bigint;

  @Column({ type: 'bigint' })
  job_id: bigint;

  @Column({ type: 'varchar', length: 255 })
  campaign_id: string;

  @Column({ type: 'datetime' })
  scheduled_time: Date;

  @Column({ type: 'varchar', length: 255 })
  channel_id: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @ManyToOne(() => Job, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;
}