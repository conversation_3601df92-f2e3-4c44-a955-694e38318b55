import { Module } from '@nestjs/common';
import { FileImportService } from './file-import.service';
import { FileImportController } from './file-import.controller';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobFilesService } from '../../jobs/job-files/job-files.service';
import { JobActionsModule } from '../../jobs/job-actions/job-actions.module';
import { JobsModule } from 'src/jobs/jobs/jobs.module';
import { JobDetailsModule } from 'src/jobs/job-details/job-details.module';
import { CompanyConfigModule } from 'src/jobs/company-config/company-config.module';
import { GenerateSignedUrlController } from './generate-signed-url.controller';
import { FileNameService } from './file-name.service';
import { JwtService } from '@nestjs/jwt';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { GenerateTemplatesController } from './generate-templates-controller';
import { EngineModule } from 'src/engine/engine.module';

@Module({
  imports: [JobActionsModule, DatabaseModule, JobsModule, JobDetailsModule, CompanyConfigModule,    FlexDatabaseModule, EngineModule],
  controllers: [FileImportController, GenerateSignedUrlController, GenerateTemplatesController],
  providers: [FileImportService, JobFilesService, GcpBucketService, FileNameService, JwtService],
})
export class FileImportModule {}

