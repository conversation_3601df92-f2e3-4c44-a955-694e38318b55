import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsString, ValidateNested, ValidationArguments } from "class-validator";

export const DEFAULT_PAGE_SIZE = 25;

class SortDetailDTO {
    //@IsString({ message: i18nValidationMessage('validation.invalid-string') })
    @IsOptional()
    field: string;
  
  //  @IsString({ message: i18nValidationMessage('validation.invalid-string') })
    @IsOptional()
    sortOrder: 'ASC' | 'DESC';
  }
  
  class FilterFieldDTO {
   // @IsString({ message: i18nValidationMessage('validation.invalid-string') })
    @IsOptional()
    field: string;
  
    // This is where the improvement is: Restrict "LIKE" to strings only
   /* @IsIn(['==', '<>', '>', '>=', '<', '<=', 'LIKE'], {
      message: i18nValidationMessage('validation.invalid-operator'),
    })
      */
    operator: '==' | '<>' | '>' | '>=' | '<' | '<=' | 'LIKE';
  
    // The "LIKE" operator should only be allowed for string fields
    @IsString({
      message: (args: ValidationArguments) =>
        args.object['operator'] === 'LIKE'
          ? 'validation.invalid-string-for-like'
          : 'validation.invalid-string',
    })
    @IsOptional()
    value: string;
  }

export class ApiResourceListDto {
    @ApiProperty({
      description: 'Number of records to return per page',
      example: '100',
    })
    @Type(() => Number)
    //@IsInt({ message: i18nValidationMessage('validation.invalid-int') })
    //@Min(1, { message: i18nValidationMessage('validation.min-value') })
    //@Max(1000, { message: i18nValidationMessage('validation.max-value') })
    pageSize?: number = DEFAULT_PAGE_SIZE;
  
    @ApiProperty({
      description: 'Page number to request',
      example: '1',
    })
    @Type(() => Number)
   // @IsInt({ message: i18nValidationMessage('validation.invalid-int') })
   // @Min(1, { message: i18nValidationMessage('validation.min-value') })
    pageNo?: number = 1;
  
    @ApiPropertyOptional({
      description: `Array of fields to sort. [{'field':'demo', 'sortOrder':'ASC | DESC'}]`,
      isArray: true,
    })
    @IsOptional()
   // @IsArray({ message: i18nValidationMessage('validation.invalid-array') })
    @ValidateNested({ each: true })
    @Type(() => SortDetailDTO)
    sort?: SortDetailDTO[];
  
    @ApiPropertyOptional({
      description: 'Array of Filters. Supports operators like "LIKE" for partial matching.',
      isArray: true,
      // type: 'filters',
    })
    @IsOptional()
   // @IsArray({ message: i18nValidationMessage('validation.invalid-array') })
    @ValidateNested({ each: true })
    @Type(() => FilterFieldDTO)
    filters?: FilterFieldDTO[];
  
    @ApiPropertyOptional({
      description: 'Logical operator to combine filters (AND/OR)',
      example: 'AND',
      default: 'AND',
    })
    @IsOptional()
    //@IsString({ message: i18nValidationMessage('validation.invalid-string') })
   //@IsIn(['AND', 'OR'], { message: i18nValidationMessage('validation.invalid-logical-operator') })
    filterLogic?: 'AND' | 'OR';
  
    @ApiPropertyOptional({
      description: 'Fetch all the rows if its true',
      example: 'True',
      type: 'boolean',
    })
    @IsOptional()
    @Type(() => Boolean)
   // @IsBoolean({ message: i18nValidationMessage('validation.invalid-boolean') })
    fetchAll?: boolean;
  }