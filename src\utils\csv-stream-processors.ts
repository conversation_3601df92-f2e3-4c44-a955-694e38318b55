import * as csvParser from 'csv-parser';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import * as async from 'async';


@Injectable()
export class CsvStreamProcessor {
  constructor() {}

  private cleaningGC = false;

    async import(
      stream: Readable, 
      onCreateRowObject: (rowData: any, rowIndex: number) => any,
      onBatchProcess: (batchData: any[]) => any,
      onHeaders: (headers: string[]) => void,
    ): Promise<void> {

    let rowIndex = 1;
    const batch: any[] = [];
    const queue = createQueueToProcessOneRowAtATime();

    const interval = setInterval(()=>{this.manageMemory(stream)}, 500);

    console.log('Streamng file.');

    return new Promise((resolve, reject) => {
      const csv = stream.pipe(csvParser());

      csv.on('headers', (headers) => {
        console.log('CSV Headers:', headers);
        
        if(onHeaders){
            onHeaders(headers);
        }
        else{
          console.log('No headers function provided.');
        }
      });

      csv.on('data', (row) => {
          this.pauseStreamIfHighMemory(stream);
          let queueItem = row;
          let index = rowIndex++;

          if (index % 100 === 0) {
            console.log(`Processed ${index} rows...`);
          }

          if (onCreateRowObject){
            queueItem = onCreateRowObject(row, index);
          }

          if (queueItem){
            queue.push(queueItem, (err) => {
              if (err) {
                console.error('Error processing row:', err);
              }
            });
          }
          else{
            console.warn('Row is empty or invalid:', row);
          };
      });

      csv.on('end', async () => {
        console.log(`Processed ${rowIndex} rows...`);
        console.log('CSV file input processing completed.');
        clearInterval(interval);

        await new Promise<void>((resolveDrain) => {
          queue.drain(() => {
            console.log('Draining queue...');
            resolveDrain();
          });
        });

        if (batch.length > 0) {
          console.log(`Processing remaining batch... ${batch.length} Queue Length: ${queue.length()}`);
          await onBatchProcess(batch);
        }

        if (queue.idle()) {
          console.log('Queue is already idle. Resolving...');
          resolve();
        }
      });

      csv.on('error', (error) => {
        clearInterval(interval);
        
        console.error(error.message);
        reject(error);
      });
    });

      function createQueueToProcessOneRowAtATime() {
        return async.queue(async (row: any, callback: () => void) => {
          try {
            batch.push(row);

            if (batch.length >= 100) {
              console.log(`Saving to DB: ${batch.length}`);

              const batchToSaveToDb = batch.splice(0, 100);
              await onBatchProcess(batchToSaveToDb);
            }

            if (callback) {
              callback();
            }
          } catch (error) {
            if (callback) {
              callback();
            }
            throw error;
          }
        }, 1);
      }
  }

  private pauseStreamIfHighMemory(stream: Readable) {
    if (process.memoryUsage().heapUsed > 400 * 1024 * 1024) {
      console.log(`Memory useage(${process.memoryUsage().heapUsed}) too high, pausing stream...`);
      stream.pause();
    }
  }

  private manageMemory (stream : Readable)  {
    console.log(`Memory usage: ${process.memoryUsage().heapUsed}, stream paused: ${stream.isPaused()}`);
   
    if (stream.isPaused()) 
    {
      if ( process.memoryUsage().heapUsed < 200 * 1024 * 1024)
      {
        stream.resume();
        console.log('Resuming stream processing...');
      }
      else{
        
        if (!this.cleaningGC){
          console.log('Memory usage exceeded 200MB. Triggering garbage collection...');
          this.cleaningGC = true;
          if (global.gc) {
            global.gc();
          } else {
            console.warn('Garbage collection is not exposed. Consider running the process with --expose-gc.');
          }
          console.log('Garbage collection Complete.');
          this.cleaningGC = false;
        }
        else{
          console.log('Garbage collection running.');
        }
        
      }
    }}
}