import { Module, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { buildTypeOrmConfig } from './database/file-drop/typeorm.config';
import { FileImportModule } from './actions/file-import/file-import.module';
import { ArgusModule } from './jobs/argus/argus.module';
import { JobActionsModule } from './jobs/job-actions/job-actions.module';
import { JobsModule } from './jobs/jobs/jobs.module';
import { JobDetailsModule } from './jobs/job-details/job-details.module';
import { CompanyConfigModule } from './jobs/company-config/company-config.module';
import { FullWashedDataModule } from './actions/full-washed-data/full-washed-data.module';
import { ContactMasterListModule } from './jobs/contact-master-list/contact-master-list.module';
import { WashWhatsAppNumbersModule } from './actions/wash-whats-app-numbers/wash-whats-app-numbers.module';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from './utils/all-exceptions.filter';
import { CheckOutputModule } from './actions/check-output/check-output.module';
import { AuthModule } from './shared/auth/auth.module';
import { ImportJobDetailsModule } from './actions/import-job-details/import-job-details.module';
import { FlexDatabaseModule } from './database/flex/flex-database.module';
import { buildFlexDataSourceConfig } from './database/flex/typeorm.config';
import { ReportsModule } from './reports/reports.module';
import { EngineModule } from './engine/engine.module';
import { DetectDuplicatesModule } from './actions/detect-duplicates/detect-duplicates.module';
import { FlexImportContactsModule } from './actions/flex-import-contacts/flex-import-contacts.module';
import { UtilModule } from './utils/util.module';
import { FlexCreateCampaignModule } from './actions/flex-create-campaign/flex-create-campaign.module';
import { FilesModule } from './api/files/files.module';
import { FlexCampaignReportModule } from './actions/flex-campaign-report.module';
import { FlexWebhookOptoutModule } from './actions/flex-webhook-optout/flex-webhook-optout.module';


@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [`.env.${process.env.NODE_ENV}`, '.env'],
    }),

    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: buildTypeOrmConfig,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: buildFlexDataSourceConfig,
    }),
    FlexDatabaseModule,
    JobActionsModule,
    JobsModule,
    FileImportModule,
    JobDetailsModule,
    CompanyConfigModule,
    ArgusModule,
    FullWashedDataModule,
    ContactMasterListModule,
    WashWhatsAppNumbersModule,
    CheckOutputModule,
    ImportJobDetailsModule,
    AuthModule,
    ReportsModule,
    EngineModule,
    DetectDuplicatesModule,
    FlexImportContactsModule,
    UtilModule,
    FlexCreateCampaignModule,
    FilesModule,
    FlexCampaignReportModule,
    FlexWebhookOptoutModule
  ],
})
export class AppModule implements OnModuleInit {
  onModuleInit() {
    console.log('Modules initialized:', this.constructor.name);
  }
}
