import { Controller, Get, UseGuards } from '@nestjs/common';
import { ActionExecutionService } from './action-execution.service';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { JobService } from 'src/jobs/jobs/jobs.service';

@UseGuards(CombinedAuthGuard)
@Controller('engine')
export class EngineController {
  constructor(
    private readonly actionExecutionService: ActionExecutionService,
    private readonly jobService: JobService
  ) {}

  @Get('run')
  async run(): Promise<void> {
    console.log('Running engine...');
    const actionsProcessed = await this.actionExecutionService.processReadyActions();
    await this.jobService.calculateStatus();
    console.log(`Engine run complete. ${actionsProcessed} actions processed.`);
  }
}