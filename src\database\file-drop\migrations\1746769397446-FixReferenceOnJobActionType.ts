import { MigrationInterface, QueryRunner } from "typeorm";

export class FixReferenceOnJobActionType1746769397446 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action
            MODIFY job_action_type_id BIGINT UNSIGNED NOT NULL;
        `);

        await queryRunner.query(`
            ALTER TABLE job_action
            ADD CONSTRAINT fk_job_action_type
            FOREIGN KEY (job_action_type_id)
            REFERENCES job_action_type(id);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action
            DROP FOREIGN KEY fk_job_action_type;
        `);
    }

}
