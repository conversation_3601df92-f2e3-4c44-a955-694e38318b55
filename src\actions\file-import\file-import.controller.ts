import { Controller, Get, Param, HttpException, HttpStatus, Post, Body, UploadedFile, UseInterceptors, Query, UseGuards, Req } from '@nestjs/common';
import { JobActionsService } from '../../database/file-drop/services/job-actions.service';
import { JobFilesService } from '../../jobs/job-files/job-files.service';
import { ApiTags, ApiParam, ApiBody, ApiResponse } from '@nestjs/swagger';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { FileTypeEnum, JobActionTypeEnum, JobActionStatusEnum, JobTypeEnum, JobSubStatusEnum, JobStatus } from 'src/database/file-drop/entities/enums';
import { FileNameService } from './file-name.service';
import { JobAction } from 'src/database/file-drop/entities/job-action.entity';
import { AudienceService } from '../../database/flex/services/audience.service';
import { UserService } from 'src/database/flex/services/user.service';
import { JobStatsService } from 'src/database/file-drop/services/job-stats.service';
import { ConfigService } from '@nestjs/config';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { ActionExecutionService } from 'src/engine/action-execution.service';
import { CompanyConfigService } from 'src/jobs/company-config/company-config.service';

const NUMBER_OF_REPORTS = "NUMBER_OF_REPORTS";
const HOURS_BETWEEN_REPORTS = "HOURS_BETWEEN_REPORTS";

@ApiTags('File Import')
@UseGuards(CombinedAuthGuard)
@Controller('file-import')
export class FileImportController {
  constructor(
    private readonly jobService: JobService,
    private readonly jobActionsService: JobActionsService,
    private readonly jobFilesService: JobFilesService,
    private readonly fileNameService: FileNameService,
    private readonly audienceService: AudienceService,
    private readonly userService: UserService,
    private readonly jobStatsService: JobStatsService,
    private readonly config: ConfigService,
    private readonly actionExecutionService: ActionExecutionService,
    private readonly companyConfigService: CompanyConfigService,
  ) {}

  @Post('createActions/contact')
  async processContactFile(@Body() bodyData: { bucket: string; fileName: string }) {
    const { bucket, fileName } = bodyData;

    if (!bucket || !fileName) {
      throw new HttpException('Bucket and fileName are required', HttpStatus.BAD_REQUEST);
    }

    const fileSections = fileName.split('/');
    const data = this.fileNameService.readContactImportFileName(fileSections[fileSections.length - 1]);
    data.companyId = fileSections[1];

    if (!data.userId)
    {
      data.userId = data.userId;
    }

    let job = await this.jobService.findByOrigonalFileName(fileName, data.companyId);

    if (!job) {
      job = await this.jobService.create({
        company_id: data.companyId,
        original_file_name: fileName,
        job_type_id:  data.jobType.toString(),
        created_by_user_id: data.userId,
        updated_by_user_id: data.userId
      });
    };

    const user = await this.userService.findOne(data.userId, data.companyId);
    let audience = null;
    
    if (data.audience_id) {
      audience = await this.audienceService.findOne(data.audience_id, data.companyId);
    }

    this.jobStatsService.upsert({job_id: job.id, created_by : user, audience});

    if (!user) {
      console.log(`User not found: ${data.userId} companyId: ${data.companyId}`);
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }
    else{
      console.log('User found:', user);
    }


    await this.jobFilesService.deleteAllFilesForJob(job.id);
    await this.jobActionsService.deleteAllActionsForJob(job.id);


    let origonalFile = await this.jobFilesService.findOrigonalFileForJob(job.id);
  
    if (!origonalFile) {
      origonalFile = await this.jobFilesService.create({
        job_id: job.id,
        file_type_id: FileTypeEnum.OrigonalFile.toString(),
        name: fileName,
        url: ``,
        created_by_user_id: data.userId,
        updated_by_user_id: data.userId,
        deleted_at: null
      });
    }

    const jobActions: Partial<JobAction>[] = [];
    
    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.ImportJobDetailsFromFile],
      status_id: JobActionStatusEnum.Ready.toString(),
      data: { fileId : origonalFile.id, fileName: origonalFile.name},
      job_action_type_id: JobActionTypeEnum.ImportJobDetailsFromFile.toString(),
      sequence:1,
      percentage_of_job:10
    });

    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.DetectDuplicates],
      job_action_type_id: JobActionTypeEnum.DetectDuplicates.toString(),
      status_id: JobActionStatusEnum.Created.toString(),
      data: { },
      trigger_event: JobActionTypeEnum.ImportJobDetailsFromFile.toString(),
      sequence:2,
      percentage_of_job:5
    });
    
    let importIntoFlexTrigger = JobActionTypeEnum.DetectDuplicates.toString();
    let gnerateImportResultFilesTrigger = JobActionTypeEnum.DetectDuplicates.toString();
    let noWashPercentageAdjust = 20;

    if (data.washData) {
      noWashPercentageAdjust = 0;

      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.WashWahatsAppNumbersData],
        job_action_type_id: JobActionTypeEnum.WashWahatsAppNumbersData.toString(),
        status_id: JobActionStatusEnum.Created.toString(),
        data: { },
        sequence:3,
        trigger_event: JobActionTypeEnum.DetectDuplicates.toString(),
        percentage_of_job:40
      });

      importIntoFlexTrigger = JobActionTypeEnum.WashWahatsAppNumbersData.toString();
      gnerateImportResultFilesTrigger = JobActionTypeEnum.WashWahatsAppNumbersData.toString();
    }

    if (data.importIntoFlex) {
      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.ImportContactsIntoFlex],
        job_action_type_id: JobActionTypeEnum.ImportContactsIntoFlex.toString(),
        status_id: JobActionStatusEnum.Created.toString(),
        data: { audience_id: data.audience_id },
        trigger_event: importIntoFlexTrigger,
        sequence:4,
        percentage_of_job:30 + noWashPercentageAdjust
      });

      gnerateImportResultFilesTrigger = JobActionTypeEnum.ImportContactsIntoFlex.toString();
    }

    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.GenerateImportResultFiles],
      job_action_type_id: JobActionTypeEnum.GenerateImportResultFiles.toString(),
      status_id: JobActionStatusEnum.Created.toString(),
      data: { },
      trigger_event: gnerateImportResultFilesTrigger,
      sequence:5,
      percentage_of_job:15 + noWashPercentageAdjust
    });

    await this.jobActionsService.createBatch(jobActions);

    await this.actionExecutionService.processReadyActions();
  } 

  @Post('createActions/campaign')
  async processCampaignFile(@Req() req,@Body() bodyData: { bucket: string; fileName: string }) {
    console.log('Processing Campaign File:', JSON.stringify(bodyData));
    const { bucket, fileName } = bodyData;

    if (!bucket || !fileName) {
      throw new HttpException('Bucket and fileName are required', HttpStatus.BAD_REQUEST);
    }
    const fileSections = fileName.split('/');
    const companyId = req.user?.companyId || fileSections[1]; 
    let userId = req.user?.userId; 

    if (!userId){
      const fistCompanyUser = await this.userService.findFirstCompanyUser(companyId);
      userId = fistCompanyUser.id;
    }
    
    const data = this.fileNameService.readCampaignImportFileName(fileSections[fileSections.length - 1], userId, companyId);

    let job = await this.jobService.findByOrigonalFileName(fileName, companyId);

    console.log(JSON.stringify(req.headers));

    if (!job) {
      job = await this.jobService.create({
        company_id: data.companyId,
        original_file_name: fileName,
        job_type_id: data.jobType.toString(),
        created_by_user_id: data.userId,
        updated_by_user_id: data.userId,
        auth_token: req.headers['authorization'],
      });
    };
    
    if (!data.scheduleDate)
    {
      await this.jobService.update(job.id, { status_id: JobStatus.Errored.toString(), job_sub_status_id: JobSubStatusEnum.InvalidScheduleDate.toString() });
      throw new HttpException('Schedule date is required', HttpStatus.BAD_REQUEST);
    }
  
    const user = await this.userService.findOne(data.userId, data.companyId);

    this.jobStatsService.upsert({job_id: job.id, created_by : user});

    if (!user) {
      console.log(`User not found: ${data.userId} companyId: ${data.companyId}`);
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }
    else{
      console.log('User found:', user);
    }

    await this.jobFilesService.deleteAllFilesForJob(job.id);
    await this.jobActionsService.deleteAllActionsForJob(job.id);


    let origonalFile = await this.jobFilesService.findOrigonalFileForJob(job.id);
  
    if (!origonalFile) {
      origonalFile = await this.jobFilesService.create({
        job_id: job.id,
        file_type_id: FileTypeEnum.OrigonalFile.toString(),
        name: fileName,
        url: ``,
        created_by_user_id: data.userId,
        updated_by_user_id: data.userId,
        deleted_at: null
      });
    }

    const jobActions: Partial<JobAction>[] = [];
    
    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.ImportJobDetailsFromFile],
      job_action_type_id: JobActionTypeEnum.ImportJobDetailsFromFile.toString(),
      status_id: JobActionStatusEnum.Ready.toString(),
      data: { fileId : origonalFile.id, fileName: origonalFile.name},
      sequence:1,
      percentage_of_job: 10
    });

    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.DetectDuplicates],
      job_action_type_id: JobActionTypeEnum.DetectDuplicates.toString(),
      status_id: JobActionStatusEnum.Created.toString(),
      data: { },
      trigger_event: JobActionTypeEnum.ImportJobDetailsFromFile.toString(),
      sequence:2,
      percentage_of_job: 5
    });
    
    let importIntoFlexTrigger = JobActionTypeEnum.DetectDuplicates.toString();
    let gnerateImportResultFilesTrigger = JobActionTypeEnum.DetectDuplicates.toString();
    let noWashPercentageAdjust = 20;

    if (data.washData) {
      noWashPercentageAdjust = 0;

      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.WashWahatsAppNumbersData],
        job_action_type_id: JobActionTypeEnum.WashWahatsAppNumbersData.toString(),        
        status_id: JobActionStatusEnum.Created.toString(),
        data: { },
        sequence:3,
        trigger_event: JobActionTypeEnum.DetectDuplicates.toString(),
        percentage_of_job: 40
      });

      importIntoFlexTrigger = JobActionTypeEnum.WashWahatsAppNumbersData.toString();
      gnerateImportResultFilesTrigger = JobActionTypeEnum.WashWahatsAppNumbersData.toString();
    }

    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.ImportContactsIntoFlex],
      job_action_type_id: JobActionTypeEnum.ImportContactsIntoFlex.toString(),             
      status_id: JobActionStatusEnum.Created.toString(),
      data: { },
      trigger_event: importIntoFlexTrigger,
      sequence:4,
      percentage_of_job: 20 + noWashPercentageAdjust
    });
   
    jobActions.push({
      job_id: job.id,
      action:JobActionTypeEnum[JobActionTypeEnum.CreateFlexCampaign],
      job_action_type_id: JobActionTypeEnum.CreateFlexCampaign.toString(),                
      status_id: JobActionStatusEnum.Created.toString(),
      data: { scheduleDate: data.scheduleDate, templateGroup: data.templateGroup,  channelId: data.channelId },
      trigger_event: JobActionTypeEnum.ImportContactsIntoFlex.toString(),
      sequence:5,
      percentage_of_job: 20 + noWashPercentageAdjust
    });

    jobActions.push({
      job_id: job.id,
      action: JobActionTypeEnum[JobActionTypeEnum.GenerateImportResultFiles],
      job_action_type_id: JobActionTypeEnum.GenerateImportResultFiles.toString(),                  
      status_id: JobActionStatusEnum.Created.toString(),
      data: { },
      trigger_event: JobActionTypeEnum.CreateFlexCampaign.toString(),
      sequence:6,
      percentage_of_job: 5
    });

    const companyConfig = await this.companyConfigService.findByCompanyIdAndKeys(companyId, [NUMBER_OF_REPORTS, HOURS_BETWEEN_REPORTS]);

    const numberOfReports = companyConfig[NUMBER_OF_REPORTS] ?? this.config.get<string>(NUMBER_OF_REPORTS);
    const hoursBetweenReports = companyConfig[HOURS_BETWEEN_REPORTS] ?? this.config.get<string>(HOURS_BETWEEN_REPORTS);

      // Set trigger_time to 1am the next day
      const now = new Date();
      const trigger_time = new Date(data.scheduleDate.getFullYear(), data.scheduleDate.getMonth(), data.scheduleDate.getDate() + 1, 1, 0, 0, 0);

      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.UpdateCampaignResults],
        job_action_type_id: JobActionTypeEnum.UpdateCampaignResults.toString(),              
        status_id: JobActionStatusEnum.Created.toString(),
        data: { scheduleDate: data.scheduleDate, templateGroup: data.templateGroup,  channelId: data.channelId },
        trigger_event: JobActionTypeEnum.CreateFlexCampaign.toString(),
        trigger_time: trigger_time,
        sequence:7,
      });

    for (let i = 0; i < parseInt(numberOfReports); i++) {
      const trigger_time = new Date(new Date(data.scheduleDate).getTime() + ((i + 1) * parseInt(hoursBetweenReports) * 60 * 60 * 1000));
      const tag = `report_${i}`;
      
      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.UpdateCampaignResults],
        job_action_type_id: JobActionTypeEnum.UpdateCampaignResults.toString(),              
        status_id: JobActionStatusEnum.Created.toString(),
        data: { scheduleDate: data.scheduleDate, templateGroup: data.templateGroup,  channelId: data.channelId },
        trigger_event: JobActionTypeEnum.CreateFlexCampaign.toString(),
        trigger_time: trigger_time,
        tag: tag,
        sequence:8 + (i * 3),
      });
  
      jobActions.push({
        job_id: job.id,
        action: JobActionTypeEnum[JobActionTypeEnum.ExportCampaignResults],
        job_action_type_id: JobActionTypeEnum.ExportCampaignResults.toString(),                     
        status_id: JobActionStatusEnum.Created.toString(),
        data: { scheduleDate: data.scheduleDate, templateGroup: data.templateGroup,  channelId: data.channelId },
        trigger_event: JobActionTypeEnum.UpdateCampaignResults.toString(),
        trigger_time: trigger_time,
        tag: tag,
        sequence:9 + (i * 3),
      });       
    }

    await this.jobActionsService.createBatch(jobActions);
    await this.actionExecutionService.processReadyActions();
  } 
}