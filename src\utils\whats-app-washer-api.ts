import { ConfigService } from '@nestjs/config';
import bottleneck from 'bottleneck';
import axios from 'axios';
import { createObjectCsvStringifier } from 'csv-writer';
import * as stream from 'stream';
const FormData = require('form-data');
import { Injectable } from '@nestjs/common';
const fs = require('fs');

@Injectable()
export class WhatsAppWasherApiService {
    private readonly API_ENDPOINTS: string[];

    constructor(config: ConfigService) {
        this.API_ENDPOINTS = config.get<string>('API_ENDPOINTS').split(',');
    }


    async getClusterStatus() {
        const output = [];

        const limiter = new bottleneck({
            maxConcurrent: 10,
            minTime: 10, 
        });

        const batchPromises = [];
        for (const endpoint of this.API_ENDPOINTS) {
            const batchPromise = this.addApiFetchToBottleneck(limiter, endpoint, output);
            batchPromises.push(batchPromise);
        }

        console.log('Checking the following APIS');
        // Wait for all tasks to complete

        await Promise.all(batchPromises);
        limiter.stop();
        console.log('Fetched all API status');
        return output;
    }


    private addApiFetchToBottleneck(limiter, endpoint, output) {
        return limiter.schedule(async () => {
            try {
                const apiData = await this.getInstanceDetails(endpoint);
                
                if (apiData) {
                    output.push(apiData);
                }
            }
            catch (error) {
                console.log(`Error fetching data form endpoint ${endpoint}`);
                throw error;
            }
        });
    }

    private async getInstanceDetails(apiEndpoint) {
        const url = `${apiEndpoint}/api/jobs`;
       
        var output = {
            available: false,
            url,
            baseUrl: apiEndpoint,
            workLog: [],
        };
       
        try {
            const response = await axios.get(url, {
                timeout: 5000, // Timeout in milliseconds (5 seconds)
            });

            const now = new Date(); // Current time
            const cutoff = new Date(now.getTime() - 72 * 60 * 60 * 1000); // 72 hours ago
            
            output.workLog =
                response.data.filter((x) => new Date(x.createdAt) > cutoff) ?? [];
            
                if (output.workLog) {
                output.available = !output.workLog.some((x) => x.status.toLowerCase() === 'processing');
                const logMessage = `${url} has ${output.workLog.length} recent jobs, cutoff ${cutoff}, available : ${output.available}`;
                console.log(logMessage);

                // activityLogService.logActivity("DataWashing", null, logMessage, context);
            }
        }
        catch (error) {
            console.error(`Failed to check API status for ${apiEndpoint}:`, error.message);
        }
        return output;
    }

    extractEpochDate = (fileName) => {
        const regex = /-(\d+)-verified\.csv$/;
        const match = fileName.match(regex);
        return match ? new Date(parseInt(match[1], 10)) : null;
    };

    async sendDataForManualWashing(apiToSendTo, dataToSend): Promise<{fileName: string, success: boolean}> {
        let success = false;

        if (dataToSend.length === 0) {
            console.log('Nothing to process');
            return;
        }

        const csvStringifier = createObjectCsvStringifier({
            header: [
                { id: 'sanitized_number', title: 'Sanitized Number' },
            ],
        });

        //console.log(JSON.stringify(dataToSend));
        //const cleanDataToSend = dataToSend.map((x) => {sanitized_number: x.sanitized_number});

        //console.log('cleanDataToSend:');
        //console.log(JSON.stringify(cleanDataToSend));

        const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(dataToSend);

        // console.log('CSV Content:', csvContent);
        // console.log(csvContent);
        
        const csvBuffer = Buffer.from(csvContent, 'utf-8');

        // Write the CSV content to a local file for checking
        //const localFilePath = `./${Date.now()}_batchSize_${dataToSend.length}.csv`;
        //fs.writeFileSync(localFilePath, csvContent, 'utf-8');
        //console.log(`CSV content written to local file: ${localFilePath}`);

        const memoryStream = new stream.PassThrough();
        memoryStream.end(csvBuffer);

        const fileName = `${Date.now()}_batchSize_${dataToSend.length}.csv`;

        // console.log(JSON.stringify(dataToSend));

        const formData = new FormData();
        formData.append('phoneNums', memoryStream, { filename: fileName });

        const logMessage = `Sending batch ${fileName} to: ${apiToSendTo.baseUrl}`;
        console.log(logMessage);

        try {
            const response = await axios.post(`${apiToSendTo.baseUrl}/api/verify`, formData, {
                headers: {
                    ...formData.getHeaders(),
                },
            });

            if (response.status >= 200 && response.status < 300) {
                success = true;
                console.log('Request was successful:', response.data);
            } else {
                console.error(`Unexpected response status: ${response.status}`);
            }
        } catch (error) {
            console.error('Error sending data to API:', error.message);
        }

        return {
            fileName,
            success
        };
    }
}

export class FileProcessingStatusDto {
    _id; // Unique identifier for the document
    filePath; // Path to the original file
    status; // Status of the file processing
    createdAt; // ISO string of when the document was created
    updatedAt; // ISO string of when the document was last updated
    processedFileURL; // URL to the processed file
    processedNumberCount; // Count of processed numbers in the file
    totalNumbers; // Total numbers in the original file
}

export class ApiStatus {
    available;
    url;
    baseUrl;
    workLog;
}