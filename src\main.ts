import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle('API Documentation')
    .setDescription('API endpoints for the application')
    .setVersion('1.0')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const configService = app.get(ConfigService);

  // Enable CORS with configurable origins
  const allowedOrigins = configService.get<string>('ALLOWED_ORIGINS') || '*';
  app.enableCors({
    origin: allowedOrigins === '*' ? true : allowedOrigins.split(','),
    exposedHeaders: ['Content-Disposition', 'Authorization', 'X-Custom-Header'],
  });

  // Get the port from the environment variables or default to 3000
  const port = configService.get<number>('HOST_PORT') || 3000;

  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Swagger is running on: http://localhost:${port}/api`);

  await app.listen(port);
}
bootstrap();
