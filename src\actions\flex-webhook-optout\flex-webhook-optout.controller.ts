import { <PERSON>, <PERSON>, Param, Req } from '@nestjs/common';
import { ContactService as FlexContactService } from 'src/database/flex/services/contact.service';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import axios from 'axios';
import { JobSubStatusEnum } from 'src/database/file-drop/entities/enums';
import { last } from 'rxjs';


@Controller('actions/:actionId/flex-webhook-optout')
export class FlexWebhookOptOutController {

constructor(
        private readonly actionProcessingService: ActionProcessingService,
        private readonly flexContactService: FlexContactService,
        private readonly jobService: JobService,
  ) {}

    @Post('')
    async update(@Req() req, @Param('actionId') actionId: string): Promise<void> {
  
      await this.actionProcessingService.process(actionId, async (action) => {

        let lastUpdatedIndex = action.job.job_data?.lastUpdatedIndex || 0;
        const company_id = action.job.company_id;
        const businessBrand =  action.job.job_data?.businessBrand ||"";
        const webhookUrl=  action.job.job_data?.webhookUrl || "";
        const batchSize =  action.job.job_data?.batchSize || 100;

        if (!company_id) {
          throw new Error('Company ID is required');
        }
  
        let continueLoop = true;
        let totalUpdated = 0;
  
        while(continueLoop) {
          const reportData = await this.flexContactService.getOptInUpdates(company_id,lastUpdatedIndex, batchSize, 'Opt-Out');
  
          if (!reportData || reportData.length === 0 || reportData.length < batchSize) {
            console.log(`Only partial batch returned for ${company_id} after ${lastUpdatedIndex}, batch size: ${reportData.length}. Stopping further updates.`);
            continueLoop = false;
          }

          const maxAuditId = reportData
            .sort((a, b) => Number(b.audit_id) - Number(a.audit_id))[0]?.audit_id ?? lastUpdatedIndex;

          if (lastUpdatedIndex < Number(maxAuditId)) {
            lastUpdatedIndex = Number(maxAuditId);
          }

          const payload = {
                "eventType": "optOut",
                "timestamp": new Date().toISOString(),
                "BusinessBrand": businessBrand,
                "contacts": reportData.map(contact => (
                {
                    "msisdn": contact.msisdn,
                    "channel": {
                        "id": contact.helpdesk_channel_id,
                        "number": contact.channel_number
                    },
                    "lastCampaign": {
                        "id": contact.campaign_id ? BigInt(contact.campaign_id) : null, 
                        "name": contact.campaign_name
                    }
                    
                }))
            };

            console.log(action.job.job_data);
            console.log(payload);
         
            const result = await axios.post(webhookUrl, payload, {
                headers: action.job.job_data?.headers || {}
            });
        
            if (result.status !== 200) {
              console.error(`Failed to send webhook data for ${company_id} after ${lastUpdatedIndex}. Status: ${result.status}`);
              return;
            }

          totalUpdated += reportData.length;
  
          console.log(`Sent ${reportData.length} records for ${company_id} after ${lastUpdatedIndex}`);
          await this.jobService.updateJobData(action.job_id, { lastUpdatedIndex: lastUpdatedIndex });
        }
  
        console.log(`Total Sent for Company : ${company_id}, Total Rows updated :${totalUpdated}`);
  
      }, JobSubStatusEnum['Sending Webhook Data']);    
    }
   
  
}