import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactWasherApiCompletedFiles } from '../../database/file-drop/entities/contact-washer-api-completed-files.entity';

@Injectable()
export class ContactWasherApiCompletedFilesService {
  constructor(
    @InjectRepository(ContactWasherApiCompletedFiles)
    private readonly repository: Repository<ContactWasherApiCompletedFiles>,
  ) {}

  async create(data: Partial<ContactWasherApiCompletedFiles>) {
    return this.repository.save(data);
  }

  async findAll() {
    return this.repository.find();
  }

  async findOne(id: string) {
    return this.repository.findOne({ where: { id } });
  }

  async findOneByUrl(url: string) {
    return this.repository.findOne({ where: { clean_file_url:url } });
  }

  async update(id: string, data: Partial<ContactWasherApiCompletedFiles>) {
    await this.repository.update(id, data);
    return this.findOne(id);
  }

  async upsert(data: Partial<ContactWasherApiCompletedFiles>) {
    try { 
      await this.repository.upsert(data, {
        conflictPaths: ['file_name'], // Specify the unique constraint field(s) here
      });
    }
    catch (error) {
      console.error('Error during upsert:', error);
      console.log(`Data: ${JSON.stringify(data)}`);
      throw error; // Rethrow the error to handle it in the calling function if needed
    }
  }

  async remove(id: string) {
    return this.repository.delete(id);
  }
}