import { MigrationInterface, QueryRunner } from "typeorm";

export class AddReportDataToJobDetails1747408557108 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_detail ADD COLUMN report_data json`);
        await queryRunner.query(`ALTER TABLE job_detail ADD COLUMN report_data_updated_at datetime`);
        await queryRunner.query(`ALTER TABLE job_detail ADD COLUMN message_id bigint unsigned`);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    }

}
