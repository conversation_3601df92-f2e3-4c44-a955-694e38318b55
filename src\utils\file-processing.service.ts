/*
import * as csvParser from 'csv-parser';
import * as xlsx from 'xlsx';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import { JobService } from '../jobs/jobs/jobs.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import * as async from 'async';
import { JobDetailsMapper } from 'src/jobs/job-details/job-details.mapper';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

@Injectable()
export class FileProcessingService {
  constructor(private readonly jobService: JobService, private jobDetailService: JobDetailsService) {}

  private cleaningGC = false;

  async importFile(importDetails : ImportDetailsDto): Promise<void> {
    if (importDetails.fileType === 'csv') {
      await this.importCsv(importDetails);
    } else if (importDetails.fileType === 'xlsx') {
      await this.importXlsx(importDetails);
    } else {
      throw new Error('Unsupported file type');
    }
  }

  private async importCsv(importDetails: ImportDetailsDto): Promise<void> {
    const { stream, jobId } = importDetails;

    let rowIndex = 1;
    const batch: any[] = [];
    const queue = async.queue(async (row: any, callback: () => void) => {
      try {
        rowIndex++;

        batch.push(JobDetailsMapper.createBatchEntry(jobId, row, rowIndex, importDetails.numberHeader, importDetails.countryHeader, importDetails.defaultCountry));

        if (batch.length >= 100) {
          console.log(`Saving to DB: ${batch.length}`);

          const batchToSaveToDb = batch.splice(0, 100); 
          await this.insertBatchIntoDatabase(batchToSaveToDb);

          console.log(`After save Batch size: ${batch.length}`);
        }
        

        if (callback){
          callback();
        }
      } catch (error) {
        if (callback){
          callback();
        }
        throw error;
      }
    }, 1); // Process one row at a time

    const interval = setInterval(()=>{this.manageMemory(stream)}, 500);

    console.log('Streamng file.');

    return new Promise((resolve, reject) => {
      const csv = stream.pipe(csvParser());

      csv.on('headers', (headers) => {
        console.log('CSV Headers:', headers);
        this.insertFirstRowIntoDatabase(headers, jobId);
      });


      csv.on('data', (row) => {
        this.pauseStreamIfHighMemory(stream);

        queue.push(row, (err) => {
          if (err) {
            console.error('Error processing row:', err);
          }
        });
      });

      csv.on('end', async () => {
        clearInterval(interval);
        
        queue.drain(async () => {
          await this.insertBatchIntoDatabase(batch);
          console.log('CSV file processed successfully');
          resolve();
        });
      });

      csv.on('error', (error) => {
        clearInterval(interval);
        
        console.error(error.message);
        reject(error);
      });
    });
  }

  private pauseStreamIfHighMemory(stream: Readable) {
    if (process.memoryUsage().heapUsed > 400 * 1024 * 1024) {
      console.log(`Memory useage(${process.memoryUsage().heapUsed}) too high, pausing stream...`);
      stream.pause();
    }
  }

  private manageMemory (stream : Readable)  {
    console.log(`Memory usage: ${process.memoryUsage().heapUsed}, stream paused: ${stream.isPaused()}`);
   
    if (stream.isPaused()) 
    {
      if ( process.memoryUsage().heapUsed < 200 * 1024 * 1024)
      {
        stream.resume();
        console.log('Resuming stream processing...');
      }
      else{
        
        if (!this.cleaningGC){
          console.log('Memory usage exceeded 200MB. Triggering garbage collection...');
          this.cleaningGC = true;
          if (global.gc) {
            global.gc();
          } else {
            console.warn('Garbage collection is not exposed. Consider running the process with --expose-gc.');
          }
          console.log('Garbage collection Complete.');
          this.cleaningGC = false;
        }
        else{
          console.log('Garbage collection running.');
        }
        
      }
    }}

  private async importXlsx(importDetails: ImportDetailsDto): Promise<void> {
    const chunks: Buffer[] = [];
    const { stream, jobId } = importDetails;
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('end', async () => {
      const buffer = Buffer.concat(chunks);
      const workbook = xlsx.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const headers = xlsx.utils.sheet_to_json(sheet, { header: 1 })[0] as string[];
      console.log('XLSX Headers:', headers);
      const rows = xlsx.utils.sheet_to_json(sheet);
      const batch: any[] = [];
      let rowIndex = 0;
      
      await this.insertFirstRowIntoDatabase(headers, jobId);

      for (const row of rows) {
        if (rowIndex != 0) {
          batch.push(JobDetailsMapper.createBatchEntry(jobId, row, rowIndex++, importDetails.numberHeader, importDetails.countryHeader, importDetails.defaultCountry));
          
          if (batch.length === 100) {
            await this.insertBatchIntoDatabase(batch);
            batch.length = 0; // Clear the batch
          }
        }
      }

      await this.insertBatchIntoDatabase(batch);

      console.log('XLSX file processed successfully');
    });
  }

  private async insertBatchIntoDatabase(batch: any[]): Promise<void> {
    // Use JobService to insert the batch into the job_detail table
    
    if (batch.length > 0) {
      await this.jobDetailService.insertJobDetails(batch);
    }
  }

  private async insertFirstRowIntoDatabase(headers: string[], jobId: string): Promise<void> {
    debugger
    
    // Use JobService to add the header row to the job table
    await this.jobService.updateJobHeaderRow(jobId, headers);
  }
}
  */