import { Modu<PERSON> } from '@nestjs/common';
import { EngineController } from './engine.controller';
import { ActionExecutionService } from './action-execution.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { JobActionsModule } from 'src/jobs/job-actions/job-actions.module';
import { JobActionsService } from 'src/database/file-drop/services/job-actions.service';
import { ActionProcessingService } from './action-processing.service';
import { JobsModule } from 'src/jobs/jobs/jobs.module';
import { JobService } from 'src/jobs/jobs/jobs.service';

@Module({
  imports: [HttpModule, ConfigModule,DatabaseModule, JobActionsModule, JobsModule],
  controllers: [EngineController],
  providers: [ActionExecutionService, JobActionsService, ActionProcessingService, ActionExecutionService, JobService],
  exports: [ActionExecutionService, ActionProcessingService, HttpModule, JobService],
})
export class EngineModule {}