import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFileDetailsToJobStats1747028050969 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_stats
            ADD COLUMN failed_file_url VARCHAR(5000);
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            ADD COLUMN success_file_url VARCHAR(5000);
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            ADD COLUMN failed_file_name VARCHAR(500);
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            ADD COLUMN success_file_name VARCHAR(500);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_stats
            DROP COLUMN failed_file_url;
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            DROP COLUMN success_file_url;
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            DROP COLUMN failed_file_name;
        `);

        await queryRunner.query(`
            ALTER TABLE job_stats
            DROP COLUMN success_file_name;
        `);
    }

}
