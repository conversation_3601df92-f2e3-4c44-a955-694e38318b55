import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobActionLocks1746765613862 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action ADD COLUMN locked_until DATE;
        `);

        await queryRunner.query(`
CREATE PROCEDURE job_actions_lock_eligible(IN lock_minutes INT)
BEGIN
    DECLARE exit handler for SQLEXCEPTION
    BEGIN
    -- Rollback on error
    ROLLBACK;
    END;

    START TRANSACTION;

    -- Create temp table
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_job_actions (
    job_id BIGINT,
    id BIGINT,
    \`data\` JSON,
    \`action\` VARCHAR(255)
    );

    -- Insert eligible rows into temp table
    INSERT INTO temp_job_actions (job_id, id, \`data\`, \`action\`)
    SELECT job_id, id, \`data\`, \`action\`
    FROM job_action
    WHERE status_id = 6
        OR (status_id = 3 AND locked_until < NOW());

    -- Lock the selected rows in job_action
    UPDATE job_action
    SET
    locked_until = DATE_ADD(NOW(), INTERVAL lock_minutes MINUTE),
    status_id = 3
    WHERE id IN (SELECT id FROM temp_job_actions);

    COMMIT;

    -- Return selected rows
    SELECT * FROM temp_job_actions;

END;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP PROCEDURE IF EXISTS job_actions_lock_eligible;`);
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN locked_until;`);
    }

}
