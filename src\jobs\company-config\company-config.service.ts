import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CompanyConfig } from '../../database/file-drop/entities/company-config.entity';

@Injectable()
export class CompanyConfigService {
  constructor(
    @InjectRepository(CompanyConfig)
    private readonly companyConfigRepository: Repository<CompanyConfig>,
  ) {}

  async create(data: Partial<CompanyConfig>): Promise<CompanyConfig> {
    const config = this.companyConfigRepository.create(data);
    return this.companyConfigRepository.save(config);
  }

  async findAll(company_id: string): Promise<CompanyConfig[]> {
    return this.companyConfigRepository.find({ where: { company_id } });
  }

  async findOne(id: string): Promise<CompanyConfig | null> {
    return this.companyConfigRepository.findOneBy({ id });
  }

  async update(id: string, data: Partial<CompanyConfig>): Promise<CompanyConfig> {
    await this.companyConfigRepository.update({id}, data);
    return this.findOne(id);
  }

  async delete(id: string): Promise<void> {
    await this.companyConfigRepository.delete({id});
  }

  async findByCompanyIdAndKeys(company_id: string, keys: string[]): Promise<CompanyConfig[]> {
    return this.companyConfigRepository.find({
      where: {
        company_id,
        key: In(keys),
      },
    });
  }
}