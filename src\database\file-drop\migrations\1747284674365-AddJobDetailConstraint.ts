import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobDetailConstraint1747284674365 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_detail ADD CONSTRAINT unique_job_id_source_row_index UNIQUE (job_id, source_row_index)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_detail DROP CONSTRAINT unique_job_id_source_row_index`);
    }

}
