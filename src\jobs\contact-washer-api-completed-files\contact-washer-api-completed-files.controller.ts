import { Controller, Get, Post, Body, Param, Put, Delete } from '@nestjs/common';
import { ContactWasherApiCompletedFilesService } from './contact-washer-api-completed-files.service';
import { ContactWasherApiCompletedFiles } from '../../database/file-drop/entities/contact-washer-api-completed-files.entity';

@Controller('contact-washer-api-completed-files')
export class ContactWasherApiCompletedFilesController {
  constructor(private readonly service: ContactWasherApiCompletedFilesService) {}

  @Post()
  create(@Body() data: Partial<ContactWasherApiCompletedFiles>) {
    return this.service.create(data);
  }

  @Get()
  findAll() {
    return this.service.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() data: Partial<ContactWasherApiCompletedFiles>) {
    return this.service.update(id, data);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.service.remove(id);
  }
}