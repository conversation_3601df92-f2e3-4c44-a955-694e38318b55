export class MemoryUtil {

    static getMemoryUsage(): { rss: number; heapTotal: number; heapUsed: number; external: number } {
        const memoryUsage = process.memoryUsage();
        return {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        };
    }

    static async pauseIfHighMemory() {
        const highMemoryUse = process.memoryUsage().heapUsed > 600 * 1024 * 1024;

        if (!highMemoryUse)
        {
            return;
        }

        while (process.memoryUsage().heapUsed > 600 * 1024 * 1024) {
            const memoryUsage = process.memoryUsage();
            console.log(`Memory usage too high: RSS=${memoryUsage.rss}, HeapTotal=${memoryUsage.heapTotal}, HeapUsed=${memoryUsage.heapUsed}, External=${memoryUsage.external}`);

            if (global.gc) {
                global.gc();
                console.log(`Garbage collection triggered. Updated memory usage: HeapUsed=${process.memoryUsage().heapUsed}`);
            } else {
                console.warn('Garbage collection is not exposed. Consider running the process with --expose-gc.');
            }

            // Wait for a short period before checking memory usage again
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log('Memory usage is now below the threshold. Resuming execution.');
    }

}
