import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';

@Entity('job_detail_archive')
export class JobDetailArchive {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: bigint;

  @Column({ type: 'bigint' })
  job_id: bigint;

  @Column({ type: 'varchar', length: 255 })
  phone_number: string;

  @Column({ type: 'bigint' })
  number_type_id: bigint;

  @Column({ type: 'bigint' })
  whatsapp_status_id: bigint;

  @Column({ type: 'varchar', length: 255 })
  sanitized_number: string;

  @Column({ type: 'datetime' })
  whatsapp_status_date: Date;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @Column({ type: 'bigint', nullable: true })
  job_detail_status_id: bigint;

  @Column({ type: 'json', nullable: true })
  row_data: object;

  @Column({ type: 'int', nullable: true })
  source_row_index: number;

  @Column({ type: 'text', nullable: true })
  system_message: string;
}