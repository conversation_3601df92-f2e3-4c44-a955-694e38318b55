import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveUniqueContraintToAllowKeepingOfArchiveData1746909158695 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_detail DROP INDEX job_id;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE UNIQUE INDEX job_id ON job_detail (\`job_id\`,\`source_row_index\`,\`phone_number\`);`);
    }

}
