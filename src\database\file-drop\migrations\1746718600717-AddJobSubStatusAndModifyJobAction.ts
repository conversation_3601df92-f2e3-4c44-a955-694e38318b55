import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobSubStatusAndModifyJobAction1746718600717 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN status`);
        await queryRunner.query(`ALTER TABLE job ADD COLUMN job_sub_status_id BIGINT`);
        await queryRunner.query(`ALTER TABLE job_stats ADD COLUMN display_status VARCHAR(500)`);
        await queryRunner.query(`CREATE TABLE job_sub_status (
            id INT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at DATETIME NULL
        )`);
        await queryRunner.query(`INSERT INTO job_sub_status (id, name) VALUES
            (1, 'Initilizing'),
            (2, 'Loading Data'),
            (3, 'Validating Data'),
            (4, 'Validating Number'),
            (5, 'Creating Contacts')`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE job_sub_status`);
        await queryRunner.query(`ALTER TABLE job_stats DROP COLUMN display_status`);
        await queryRunner.query(`ALTER TABLE job DROP COLUMN job_sub_status_id`);
        await queryRunner.query(`ALTER TABLE job_action ADD COLUMN status VARCHAR(255)`);
    }

}
