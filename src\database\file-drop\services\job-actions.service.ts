import { HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { JobAction } from '../entities/job-action.entity';
import { JobActionStatusEnum, JobActionTypeEnum } from 'src/database/file-drop/entities/enums';
import { ActionToProcess } from '../../../jobs/job-actions/dtos/action-to-process';
import { Action } from 'rxjs/internal/scheduler/Action';
import { UpdateJobActionsStatusDto } from 'src/jobs/job-actions/dtos/status-change.dto';

@Injectable()
export class JobActionsService {


  constructor(
    @InjectRepository(JobAction)
    private readonly jobActionRepository: Repository<JobAction>,
  ) {}

  async findAll(): Promise<JobAction[]> {
    return this.jobActionRepository.find();
  }

  async findOne(id: string): Promise<JobAction | null> {
    const jobAction = this.jobActionRepository.findOne({ where: { id }, relations: ['job'] });
    
    if (!jobAction) {
      throw new HttpException('Job action not found', HttpStatus.NOT_FOUND);
    }

    return jobAction
  }

  async setStatus(actionId: string, newStatus: JobActionStatusEnum) : Promise<boolean> {
    console.log(`Setting status of action ${actionId} to ${newStatus}`);

    const result = await this.jobActionRepository.update({ id: actionId }, { status_id: newStatus.toString() });

    return result.affected > 0;
  }

  async addDataToTaggedActions(jobId : string, tag: string, data: any) : Promise<boolean> {
    
    const actionToUpdate = await this.jobActionRepository.find({
      where: { job_id: jobId, tag: In([tag]) }
    });

    for (const action of actionToUpdate) {
      action.data = { ...action.data, ...data };
      await this.jobActionRepository.update({ id: action.id }, { data: action.data });
    }

     return true;
    }

  async startAction(actionId: string, newStatus: JobActionStatusEnum, targetStatus: JobActionStatusEnum[]) : Promise<boolean> {
    const result = await this.jobActionRepository.update(
      { id: actionId, status_id: In(targetStatus.map(status => status.toString())) }, 
      { status_id: newStatus.toString(), 
        started_at: new Date() }
    );

    const successful =  result.affected > 0;

    if (!successful) {
      throw new Error('Unable to set action status to processing');
    }
    return successful;
  }

  async completeAction(action: JobAction, lockDuration: number, status: string = JobActionStatusEnum.Completed.toString(), trigger_time: Date = null) : Promise<JobAction[]> {

    const result = await this.jobActionRepository.update({ id: action.id }, { status_id: status, completed_at: new Date(), trigger_time });

    if (result.affected === 0) {
      throw new Error('Unable to set action status to completed');
    }
    else{
      const actionsToUpdate = await this.jobActionRepository.find({ where: { job_id: action.job_id, trigger_event: action.job_action_type_id, status_id: JobActionStatusEnum.Created.toString() }, relations: ['job'] });
      const actionsToProcess = [];

      for (const action of actionsToUpdate) {
        if (action.locked_until && action.trigger_time > new Date()) {
          await this.jobActionRepository.update({ id: action.id }, { status_id: JobActionStatusEnum.Ready.toString() }); // Lock the action for processing for 1 minute
        }
        else
        {
          await this.jobActionRepository.update({ id: action.id }, { status_id: JobActionStatusEnum.Processing.toString(), locked_until: new Date(Date.now() + (lockDuration * 1000)), started_at: new Date() }); // Lock the action for processing for 1 minute
          actionsToProcess.push(action);
        }
      }

      if (actionsToUpdate.length === 0) {
        console.log(`No actions Triggered for Action :${action.id} for Job : ${action.job_id}`);
      }

      if (actionsToProcess.length === 0) {
        console.log(`No actions To process for Action :${action.id} for Job : ${action.job_id}`);
      }

      return actionsToProcess;
    }
  }

  async create(jobAction: JobAction): Promise<JobAction> {
      return this.jobActionRepository.save(jobAction);
  }

  async createBatch(jobAction: Partial<JobAction>[]): Promise<JobAction[]> {
      return this.jobActionRepository.save(jobAction);
  }

  async update(id: string, jobAction: Partial<JobAction>): Promise<void> {
    await this.jobActionRepository.update(id, jobAction);
  }

  async remove(id: string): Promise<void> {
    await this.jobActionRepository.delete(id);
  }

  async deleteAllActionsForJob(jobId: string): Promise<void> {
    await this.jobActionRepository.update({ job_id: jobId }, { deleted_at: new Date().toISOString() });
  } 

  async fetchActionsToProcess(coolDown: number, lockDuration: number): Promise<ActionToProcess[]> {
    const queryRunner = this.jobActionRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();

    try {
      // Call the stored procedure with the lock duration
      
      // Cool down is how long before the action will be sent again for processing, This is to help if there is a failer on the engine and a 
      // action may not have been sent for processing. When a Action starts processing the status is changed to procesing.

      // Lock duration is how long the action will be locked for processing. 

      const actionsToRun = await queryRunner.query(
        `CALL job_actions_lock_and_fetch_ready(${coolDown},${lockDuration});`
      );

      // Map the result to the expected format
      return actionsToRun[0].map((row: any) : ActionToProcess => ({
        job_id: row.job_id,
        actionId: row.id,
        data: row.data,
        action: row.action,
        actionType: Number(row.job_action_type_id) as JobActionTypeEnum,
        authToken: row.auth_token,
      }));
    } catch (error) {
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setStatusForJobActions(
    jobId: string,
    actionId: string,
    updateDto: UpdateJobActionsStatusDto,
  ): Promise<void> {
    const jobAction = await this.jobActionRepository.find({
      where: { id: actionId, job_id: jobId },
    });

    for (const action of jobAction) {
      action.status_id = updateDto.status_id.toString();
      action.id = action.id;
    }

    if (!jobAction) {
      throw new NotFoundException(
        `Job action ${actionId} not found for job ${jobId}`,
      );
    }
    await this.jobActionRepository.save(jobAction);
  }
}