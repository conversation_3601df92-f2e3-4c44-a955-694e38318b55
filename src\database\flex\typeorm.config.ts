// src/config/typeorm.config.ts
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { LoggerService } from 'src/utils/logger.service';
import { DataSource, DataSourceOptions } from 'typeorm';

export const buildFlexDataSourceConfig = (config: ConfigService): DataSourceOptions => ({
  type: 'mysql',
  host: config.get<string>('FLEX_DB_HOST'),
  port: config.get<number>('FLEX_DB_PORT', 3306),
  username: config.get<string>('FLEX_DB_USERNAME'),
  password: config.get<string>('FLEX_DB_PASSWORD'),
  database: config.get<string>('FLEX_DB_DATABASE'),
  entities: [__dirname + '/../**/flex/**/*.entity{.ts,.js}'],
  synchronize: false,
  migrationsRun: false,
});

export const flexDataSourceFactory = {
  provide: 'FLEX_DATABASE',
  useFactory: async (configService: ConfigService) => {
    console.log('FlexDataSource Config:', buildFlexDataSourceConfig(configService));
    const dataSource = new DataSource(buildFlexDataSourceConfig(configService));
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }
    return dataSource;
  },
  inject: [ConfigService],
};


