import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ResultOptinStatusChangeDto } from '../dto/result-optin-status-change.dto';

@Injectable()
export class ContactService {


  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {

  }

    async getOptInUpdates(company_id: string, lastUpdatedIndex: any, batchSize: number, status: string) : Promise<ResultOptinStatusChangeDto[]> {
        const results = await this.flexDataSource.query(
            `CALL contact_optin_audit_fetch_batched(?, ?, ?, ?);`,
                [
                    company_id,
                    lastUpdatedIndex,
                    batchSize,
                    status
                ]
        );

        return results[0];
  }

  async upsertContact(
    companyId: bigint,
    mobile: string,
    userId: bigint,
    firstName: string,
    surname: string,
    country: string,
    countryCode: string,
    preferredChannel: number,
    optinStatus: string,
    serverFileName: string,
    rowNumber: string,
    contactData: string,
    audiencesId?: bigint
): Promise< { duplicate_detail : string, contact_id :string}> {
    const queryRunner = this.flexDataSource.createQueryRunner();
    await queryRunner.connect();

    try {
        // Define the OUT parameter variables in MySQL session
        await queryRunner.query(`SET @out_is_duplicate_detail = '';`);
        await queryRunner.query(`SET @out_contact_id = '';`);

        // Call the stored procedure
        await queryRunner.query(
            `CALL contacts_Upsert(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @out_is_duplicate_detail, @out_contact_id);`,
            [
                companyId,
                mobile,
                userId,
                firstName,
                surname,
                country,
                countryCode,
                preferredChannel,
                optinStatus,
                audiencesId,
                serverFileName,
                rowNumber,
                contactData
            ]
        );

        // Retrieve the OUT parameter values
        const result = await queryRunner.query(`SELECT @out_is_duplicate_detail AS out_is_duplicate_detail, @out_contact_id AS out_contact_id;`);

        // Extract the output value
        return { duplicate_detail : result[0].out_is_duplicate_detail, contact_id : result[0].out_contact_id};
    } catch (error) {
        console.error('Error executing stored procedure:', error);
        return null;
    } finally {
        await queryRunner.release();
    }
}
}