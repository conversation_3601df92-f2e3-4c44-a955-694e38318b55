export class FlexImportJobDto {
  id: string;
  fileName: string;
  status: string;
  percentageComplete: string;
  failureCount: number;
  successCount: number;
  duplicateCount: number;
  failedFileUrl: string;
  successFileUrl: string;
  failureFileName: string;
  processingStartDate: string | null;
  processingEndDate: string | null;
  audience: {
    id: string;
    name: string;
  };
  updatedBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  updatedAt: string;
}