import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JobActionTypeEnum } from 'src/database/file-drop/entities/enums';
import { JobActionsService } from 'src/database/file-drop/services/job-actions.service';
import { ActionToProcess } from 'src/jobs/job-actions/dtos/action-to-process';


@Injectable()
export class ActionExecutionService {
    constructor(
        private readonly httpService: HttpService,
        private readonly config: ConfigService,
    private readonly jobActionsService: JobActionsService,
    ){}

        getUrlForAction(action :ActionToProcess): string {
            const baseUrl = this.config.get<string>('API_BASE_URL');
            const jobId = action.job_id;

        switch (action.actionType) {
            case JobActionTypeEnum.ImportJobDetailsFromFile:
            {
                if (!action.data['fileId']){
                    throw new HttpException('fileId is required', HttpStatus.BAD_REQUEST);
                } 
                
                console.log(`Triggering File Import Action ${action.actionId} with File fileId: ${action.data['fileId']}`);
                return `${baseUrl}/actions/${action.actionId}/job-details/import-file/${action.data['fileId']}`; 
            }
            case JobActionTypeEnum.DetectDuplicates:
            {                
                return `${baseUrl}/actions/${action.actionId}/detect-duplicates`; 
            }
            case JobActionTypeEnum.ExportWashWahatsAppNumbersData:
            {                
                return `${baseUrl}/actions/full-washed-data/export/${jobId}`; 
            }
            case JobActionTypeEnum.ImportContactsIntoFlex:
              {
                return `${baseUrl}/actions/${action.actionId}/flex-import-contacts`;          
              }
              case JobActionTypeEnum.CreateFlexCampaign:
                {
                  return `${baseUrl}/actions/${action.actionId}/flex-create-campaign`;          
                }
            case JobActionTypeEnum.GenerateImportResultFiles:
              {
                return `${baseUrl}/actions/${action.actionId}/flex-import-contacts/generate-result-files`;                 
              }
                          case JobActionTypeEnum.UpdateCampaignResults:
              {
                return `${baseUrl}/actions/${action.actionId}/flex-campaign-report/update`;                 
              }
              case JobActionTypeEnum.ExportCampaignResults:
              {
                return `${baseUrl}/actions/${action.actionId}/flex-campaign-report/Export`;                 
              }
              case JobActionTypeEnum.FlexWebHookOptoutUpdates:
              {
                return `${baseUrl}/actions/${action.actionId}/flex-webhook-optout`;                 
              }
            default:
              {
                throw new Error(`No URL defined for action type: ${action.actionType}`);
              }
        }
    }

  executeAction(action : ActionToProcess): void {
    console.log('Executing action:', JSON.stringify(action));

    try {
        const apiUrl = this.getUrlForAction(action);

        // Fire and forget, these calls can take 60 min and will be retried after that if failed.
        console.log(`Calling API: ${apiUrl} with data: ${JSON.stringify(action.data)}`);

        let authToken = action.authToken;

        if (!authToken) {
            const username = 'ActionsAPI';
            const password = process.env.SECRET_KEY;
            authToken = 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64');
            console.log(`No auth token provided, using default token: ${authToken}`);
        }
        
        this.httpService.post(apiUrl, action.data, {
            headers: {
              Authorization: `${authToken}`,
            },
          }).subscribe({
            next: (response) => {
              console.log(`API call succeeded for action ${action.actionId}:`, response.data);
            },
            error: (error) => {
              console.error(`API call failed for action ${action.actionId}:`, error.message);
            },
          });

      } catch (error) {
        console.error(`Failed to call API for action ${action.actionId}:`, error);
      }
  }

  async processReadyActions(): Promise<number> {   
    const readyActions = await this.jobActionsService.fetchActionsToProcess(
        this.config.get<number>('COOL_DOWN_IN_SECONDS'),
        this.config.get<number>('LOCK_DURATION_IN_MINUTES')
    );

    console.log(`Processing ${readyActions.length} ready actions...`);

    for (const action of readyActions) {     
        this.executeAction(action);
    }

    return readyActions.length;
  }
}