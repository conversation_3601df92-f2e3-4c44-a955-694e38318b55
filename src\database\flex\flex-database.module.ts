import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Audience } from './entities/audience.entity';
import { flexDataSourceFactory } from './typeorm.config';
import { UserService } from './services/user.service';
import { AudienceService } from './services/audience.service';
import { Templates } from './entities/templates.entity';
import { TemplateGroupsService } from './services/template-groups.service';
import { TemplateGroup } from './entities/template-groups.entity';
import { TemplateGroupTemplate } from './entities/template-group-template.entity';
import { ContactService } from './services/contact.service';
import { Channels } from './entities/channels.entity';
import { ChannelService } from './services/channel.service';
import { CampaignService } from './services/campaign.service';
import { CampaignAudienceService } from './services/campaign-audience.service';
import { MessageService } from './services/message.service';
import { TemplateService } from './services/template.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Audience, Templates, TemplateGroup, TemplateGroupTemplate, Channels]),
  ],
  providers: [
    flexDataSourceFactory, UserService, AudienceService, TemplateGroupsService, ContactService, ChannelService, CampaignService, CampaignAudienceService, MessageService
  ,TemplateService],
  exports: [TypeOrmModule, 'FLEX_DATABASE', UserService, AudienceService, TemplateGroupsService, ContactService, ChannelService, CampaignService, CampaignAudienceService, MessageService, TemplateService],
})
export class FlexDatabaseModule {}