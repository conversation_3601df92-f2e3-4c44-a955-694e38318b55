import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewJobActionStatus1747288085817 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`INSERT INTO job_action_status (id, name) values (5, 'QueuedForProcessing')`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DELETE FROM job_action_status WHERE id = 5 AND name = 'QueuedForProcessing'`);
    }

}
