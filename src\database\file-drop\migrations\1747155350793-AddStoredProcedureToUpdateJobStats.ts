import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStoredProcedureToUpdateJobStats1747155350793 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
CREATE PROCEDURE job_calculate_status(IN in_job_id BIGINT)
BEGIN
  WITH state_report AS (
    SELECT  
      ja.job_id,
      COUNT(ja.id) AS Total,
      COUNT(CASE WHEN ja.status_id = 4 THEN 1 ELSE NULL END) AS Total_Complete,
      COUNT(CASE WHEN ja.status_id = 100 THEN 1 ELSE NULL END) AS Total_Errored,
      COUNT(CASE WHEN ja.status_id = 101 THEN 1 ELSE NULL END) AS Total_Paused,
      COUNT(CASE WHEN ja.status_id = 102 THEN 1 ELSE NULL END) AS Total_Stopped,
      COUNT(CASE WHEN ja.status_id = 2 THEN 1 ELSE NULL END) AS Total_Ready,
      SUM(CASE WHEN ja.status_id = 4 THEN ja.percentage_of_job ELSE NULL END) AS percentage_complete,
      j.job_sub_status_id
    FROM job_action ja 
    INNER JOIN job j ON ja.job_id = j.id
    WHERE 
      (
        in_job_id IS NULL
        AND (j.status_id <> 4 OR j.status_id IS NULL)
        AND j.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
      )
      OR ja.job_id = in_job_id
    GROUP BY ja.job_id
  ),
  job_status_update AS (
    SELECT 
      job_id,
      CASE 
        WHEN Total_Complete = Total THEN 4
        WHEN Total_Paused > 0 AND Total_Ready = 0 THEN 101
        WHEN Total > (Total_Errored + Total_Paused + Total_Stopped) THEN 3
        WHEN Total_Errored > 0 THEN 100
        WHEN Total_Stopped > 0 AND Total_Ready = 0 THEN 102
        ELSE 3
      END AS NewJobStatus,
   	  CASE WHEN Total_Complete = Total  	THEN 100
	   	  	ELSE percentage_complete
	  END AS percentage_complete,
	  CASE WHEN  in_job_id IS NULL THEN job_sub_status_id ELSE NULL END AS new_job_sub_status_id
    FROM state_report
    WHERE Total > 0
  )

  UPDATE job j
  JOIN job_status_update jsu ON j.id = jsu.job_id
  SET j.status_id = jsu.NewJobStatus,
  		j.percentage_complete = jsu.percentage_complete,
  		j.job_sub_status_id = new_job_sub_status_id
  WHERE jsu.NewJobStatus IS NOT NULL;
END;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP PROCEDURE IF EXISTS job_calculate_status;`);
    }

}
