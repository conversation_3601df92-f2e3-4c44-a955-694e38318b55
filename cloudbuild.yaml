steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/flex-file-drop-actions-api', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/flex-file-drop-actions-api']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['run', 'deploy', 'flex-file-drop-actions-api', '--image', 'gcr.io/$PROJECT_ID/flex-file-drop-actions-api', '--platform', 'managed', '--region', 'europe-west1']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['run', 'services', 'add-iam-policy-binding', 'flex-file-drop-actions-api', '--member=allUsers', '--role=roles/run.invoker', '--region', 'europe-west1']
images:
  - 'gcr.io/$PROJECT_ID/flex-file-drop-actions-api'
options:
  logging: CLOUD_LOGGING_ONLY