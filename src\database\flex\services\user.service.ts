import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { User } from '../entities/user.entity';


@Injectable()
export class UserService {

  private readonly audienceRepository: Repository<User>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.audienceRepository = this.flexDataSource.getRepository(User);
  }

  async findOne(id: string, companyId: string): Promise<User | null> {
    return this.audienceRepository.findOne({ where: { id, companyId } });
  }

  async findFirstCompanyUser(companyId: any): Promise<User | null> {
    return this.audienceRepository.findOne({ where: { companyId }, order: { createdAt: 'ASC' } });
  }

}