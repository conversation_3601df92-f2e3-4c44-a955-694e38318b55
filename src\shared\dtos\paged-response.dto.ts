import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class PagedResponseDto<T> {
  @ApiProperty({
    description: 'Number of records to return per page.',
    example: 10,
  })
  @Expose()
  pageSize: number;

  @ApiProperty({
    description: 'Page number for data returned.',
    example: 1,
  })
  @Expose()
  pageNo: number;

  @ApiProperty({
    description: 'Total number of records.',
    example: 100,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Paged Data Requested, this is the same type as returned by /ID request',
    isArray: true,
  })
  @Expose()
  data: Array<T>;

  constructor(pageSize: number, pageNo: number, total: number, data: Array<T>) {
    this.pageSize = pageSize;
    this.pageNo = pageNo;
    this.total = total;
    this.data = data;
  }
}
