import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanyConfig } from '../../database/file-drop/entities/company-config.entity';
import { CompanyConfigService } from './company-config.service';
import { CompanyConfigController } from './company-config.controller';

@Module({
  imports: [TypeOrmModule.forFeature([CompanyConfig])],
  controllers: [CompanyConfigController],
  providers: [CompanyConfigService],
  exports: [CompanyConfigService],
})
export class CompanyConfigModule {}