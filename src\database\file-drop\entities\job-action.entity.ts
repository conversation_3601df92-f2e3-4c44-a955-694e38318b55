import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, DeleteDateColumn } from 'typeorm';
import { Job } from './job.entity';

@Entity('job_action')
export class JobAction {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'bigint' })
  job_id: string;

  @Column({ type: 'varchar', length: 255 })
  action: string;

  @Column({ type: 'int' })
  sequence: number;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @Column({ type: 'datetime', nullable: true })
  trigger_time: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  trigger_event: string;

  @Column({ type: 'bigint' })
  status_id: string;

  @Column({ type: 'bigint' })
  job_action_type_id: string;

  @Column({ type: 'json', nullable: true })
  data: any;

  @Column({ type: 'varchar', length: 255, default: '' })
  tag: string;
  
  @Column({ type: 'int', default: 0 })
  percentage_of_job: number

  @Column({ type: 'datetime', nullable: true })
  started_at: Date;

  @Column({ type: 'datetime', nullable: true })
  completed_at: Date;

  @Column({ type: 'int', nullable: true })
  repeat_after_x_seconds: number; 

  @ManyToOne(() => Job, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  locked_until: Date;
  
}