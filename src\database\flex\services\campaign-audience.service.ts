import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { CampaignAudience } from '../entities/campaign-audience.entity';


@Injectable()
export class CampaignAudienceService {

  private readonly campaignAudienceRepository: Repository<CampaignAudience>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.campaignAudienceRepository = this.flexDataSource.getRepository(CampaignAudience);
  }

  async createBatch(campaignAudiences: Partial<CampaignAudience>[]): Promise<CampaignAudience[]> {
    return this.campaignAudienceRepository.save(campaignAudiences);
  }
}