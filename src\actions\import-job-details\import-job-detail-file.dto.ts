import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString, Matches } from "class-validator";
import { Readable } from "stream";

export class ImportJobDetailFileDto {
  @IsNotEmpty()
  stream: Readable;

  @IsEnum(['csv', 'xlsx'])
  @IsNotEmpty()
  fileType: 'csv' | 'xlsx';

  @IsString()
  @Matches(/^\d+$/, { message: 'ID must be a string representing a non-negative integer' })
  @IsNotEmpty()
  jobId: string;

  @ApiProperty({ description: 'Header for phone numbers in the file' })
  @IsString()
  @IsNotEmpty()
  numberHeader: string;

  @ApiProperty({ description: 'Header for country codes in the file'})
  @IsOptional()
  @IsString()
  countryHeader?: string | null;

  @ApiProperty({ description: 'Default country code if not provided in the file' })
  @IsString()
  @IsNotEmpty()
  defaultCountry: string;

  fileName: string;
  companyId: string;
}