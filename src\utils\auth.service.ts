import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  validateToken(token: string): any {
    try {
      const secret = this.configService.get<string>('ACCESS_TOKEN_SECRET');
      const payload = this.jwtService.verify(token, { secret });

      return {
        userId: payload.sub,
        companyId: payload.companyId,
        email: payload.email,
        firstName: payload.firstName,
        lastName: payload.lastName,
        roles: payload.roles,
        prepaid: payload.prepaid,
        timezone: payload.timezone,
      };
    } catch (error) {
      throw new Error('Invalid token');
    }
  }
}

export interface IJwtPayload {
    sub: bigint;
    companyId: bigint;
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
    channelId?: bigint;
    prepaid: boolean;
    timezone: string;
  }
  
  export interface ITokens {
    accessToken: string;
    refreshToken: string;
  }
  