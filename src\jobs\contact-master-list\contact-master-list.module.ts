import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactMasterListService } from './contact-master-list.service';
import { ContactMasterListController } from './contact-master-list.controller';
import { ContactMasterList } from '../../database/file-drop/entities/contact-master-list.entity';
import { GcpBucketService } from '../../utils/gcp-bucket.service';
import { CsvStreamProcessor } from '../../utils/csv-stream-processors';

@Module({
  imports: [TypeOrmModule.forFeature([ContactMasterList])],
  controllers: [ContactMasterListController],
  providers: [ContactMasterListService, GcpBucketService, CsvStreamProcessor],
})
export class ContactMasterListModule {}