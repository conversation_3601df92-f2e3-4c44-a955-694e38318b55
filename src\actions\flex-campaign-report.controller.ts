import { <PERSON>, <PERSON>, Param, Req } from '@nestjs/common';
import { FileTypeEnum, JobDetailStatus, JobSubStatusEnum, WhatsAppStatus } from 'src/database/file-drop/entities/enums';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { CampaignService } from 'src/database/flex/services/campaign.service';
import { CsvExporterService } from 'src/utils/csv-exporter.service';
import { JobDetail } from 'src/database/file-drop/entities/job-detail.entity';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { CompanyConfigService } from 'src/jobs/company-config/company-config.service';
import { ConfigKeys } from 'src/jobs/company-config/config-keys';
import { JobService } from 'src/jobs/jobs/jobs.service';

const REPORT_HEADERS = ['status', 
 'statusUpdatedTime', 
 'campaignId', 
 'audience', 
 'scheduledTime', 
 'messageId', 
 'delivered', 
 'read', 
 'invalidNumber', 
 'deliveryFailed', 
 'error', 
 'optOut', 
 'file_id',
 'contact_id'];

@Controller('actions/:actionId/flex-campaign-report')
export class FlexCampaignReportController { 
constructor(
        private readonly actionProcessingService: ActionProcessingService,
        private readonly jobDetailsService: JobDetailsService,
        private readonly campaignService: CampaignService,
        private readonly csvExporter: CsvExporterService,        
        private readonly jobfilesService: JobFilesService,
        private readonly companyConfigService: CompanyConfigService,
        private readonly jobService: JobService,
  ) {}

  @Post('Update')
  async update(@Req() req, @Param('actionId') actionId: string): Promise<void> {
    const validStatusIds : string[] = [JobDetailStatus.Inserted.toString(), JobDetailStatus.PreProcessingSkippedDueToCutOffTime.toString(), JobDetailStatus.WashedViaApi.toString()]

    await this.actionProcessingService.process(actionId, async (action) => {
      const campaignId = action.data?.campaignId ?? action.job.job_data?.campaignId;
      const lastReportUpdatedAt = action.job.job_data?.lastReportUpdatedAt;
      
      let updatesAfter = typeof lastReportUpdatedAt === 'string' ? new Date(lastReportUpdatedAt) : lastReportUpdatedAt;

      if (!campaignId) {
        throw new Error('Campaign ID is required');
      }

      let continueLoop = true;
      const batchSize = 10000;
      let totalUpdated = 0;

      while(continueLoop) {
        const reportData = await this.campaignService.getReportData(campaignId, updatesAfter, batchSize);

        if (!reportData || reportData.length === 0 || reportData.length < batchSize) {
          console.log(`Only partial batch returned for ${campaignId} after ${updatesAfter}, batch size: ${reportData.length}. Stopping further updates.`);
          continueLoop = false;
        }

        for (const report of reportData) {
            let reportRecordUpdatedAt = typeof report.statusUpdatedTime === 'string' ? new Date(report.statusUpdatedTime) : report.statusUpdatedTime;

            if (reportRecordUpdatedAt > updatesAfter || !updatesAfter) {
              updatesAfter = reportRecordUpdatedAt;
            }

            await this.jobDetailsService.updateBatchByMessageId({ 
            message_id : report.message_id,
            report_data: report,
            report_data_updated_at: new Date()
          });
        }

        totalUpdated += reportData.length;

        console.log(`Updated ${reportData.length} records for campaign ${campaignId} after ${updatesAfter}`);
        await this.jobService.updateJobData(action.job_id, { lastReportUpdatedAt: updatesAfter });
      }

      console.log(`Total updated records for campaign : ${campaignId}, Total Rows updated :${totalUpdated}`);

    }, JobSubStatusEnum['Creating Campaign']);    
  }

    @Post('Export')
  async export(@Req() req, @Param('actionId') actionId: string): Promise<void> {
       await this.actionProcessingService.process(actionId, async (action) => {
            const original_file_name = action.job.original_file_name
            ? action.job.original_file_name.split(/[\\/]/).pop()?.replace(".csv", "") || ""
            : "";

          const timeStamp = Date.now().toString().substring(0, 10);
          const fileName = `export/${action.job.company_id}/campaign-report/${original_file_name}_report_${timeStamp}.csv`;
          const originalFile = await this.jobfilesService.findOrigonalFileForJob(action.job_id);

          const companyConfig = await this.companyConfigService.findByCompanyIdAndKeys(action.job.company_id, [ConfigKeys.ReportHeaders]);
          const forceReportHeaders = companyConfig.find( x => x.key === ConfigKeys.ReportHeaders)?.value;

          console.log(`Exporting to ${fileName}`);
  
          await this.csvExporter.exportToCsv(
              fileName,
              async (lastIndex) => {
                  return await this.jobDetailsService.findAllByJobIdBatched(action.job_id, 100, lastIndex, null, null);
              },
              () => {
                    if (forceReportHeaders){
                      return forceReportHeaders.split(',');
                    }

                    const originalHeaders = action.job.file_headers.split(',');
                    const combinedHeaders = [...originalHeaders, ...REPORT_HEADERS.filter(h => !originalHeaders.includes(h))];
                    return combinedHeaders;
              },
              (row : any) => {
                      const jobDetailRow = row as JobDetail;
                      const output = jobDetailRow.row_data;
                      
                      const reportData = jobDetailRow.report_data as any;
                      if (reportData && typeof reportData === 'object') {
                      Object.assign(output, reportData);
                      }


                      output["audience"] = original_file_name;
                      output["invalidNumber"] = (!(reportData?.delivered) && row.status_id === WhatsAppStatus.InvalidNumber) ? 1 : 0;
                      output["deliveryFailed"] = !(reportData?.delivered) ? reportData?.failed : null;
                      

                      output["file_id"] = originalFile.id

                      this.forceStringBooland(output,"delivered");
                      this.forceStringBooland(output,"read");
                      this.forceStringBooland(output,"invalidNumber");
                      this.forceStringBooland(output,"deliveryFailed");
                      this.forceStringBooland(output,"optOut");

                      if (output["read"] === 'TRUE' && output["delivered"] === 'FALSE')
                      {
                        output["delivered"] = 'TRUE';
                      }

                      if (!output["messageId"])
                      {
                        output["messageId"] = reportData?.message_id;
                      }

                      output["status"] = this.getMessageStatusAsName(
                        reportData?.message_state_id,
                        output["optOut"],
                        output["read"],
                        output["delivered"]
                      ).toUpperCase();

                      if (output["status"] === "FAILED")
                      {
                        output["status"] = "SEND FAILED"
                      }

                      if (reportData?.display_name &&  output["status"] == "SEND FAILED")
                      {
                        let detail = 'Message failed to send.';

                        if (!reportData?.details?.includes("Flex-Loader-service logs") &&
                          !reportData?.details?.includes("Code: 429") &&
                          !reportData?.details?.includes("SweeshIP") &&
                          !reportData?.details?.includes("Smooch") 
                        )
                        {
                          detail = reportData?.details?.replace(/,/g, '');
                        }
                        
                        output["error"] = `${reportData?.display_name} : ${detail}`;
                      }
                      else {
                        output["error"] = ``;
                      }


                      return output; 
              });
  
              await this.jobfilesService.create({ 
                  job_id: action.job_id, name: fileName, 
                  created_by_user_id: action.job.created_by_user_id, 
                  updated_by_user_id: action.job.created_by_user_id, 
                  file_type_id: 
                  FileTypeEnum.CampaignReportFile.toString(), url:''});
            
      
          }, JobSubStatusEnum['Creating Results File']);
  
  
      }

      private forceStringBooland(output : any, field: string) {
        const value = output[field]?.toString().toLowerCase() ?? "";
        
        if (value === "1" || value === "true") {
          output[field] = 'TRUE';
        } else if (value === "0" ||value === "false") {
          output[field] = 'FALSE';
        }
        else{
          output[field] = '';
        }
      }
      
    private getMessageStatusAsName(status: string | number,optOut: string, read: string, delivered: string): string {
      if (typeof status !== 'string') {
        status = status?.toString();
      }

      if (optOut === "TRUE")
        return "OPT_OUT";

      if (read === "TRUE")
        return "READ";

      if (delivered === "TRUE")
        return "DELIVERED";

      switch (status) {
        case "1":
          return 'Sent';

                  case "2":
          return 'Save';

                  case "3":
          return 'Scheduled';
                  case "4":
          return 'Queued';

                  case "5":
          return 'SENT';

                  case "6":
          return 'FAILED';


                            case "7":
          return 'Rejected - Missing Contact Data';

                            case "8":
          return 'Rejected - Not on Whitelist';

                            case "9":
          return 'Send Failed';

                            case "21006":
          return 'Opt-Out';

                                      case "21007":
          return 'Excluded';

                                      case "21008":
          return 'Opt-Out Before Send';
       
        default:
          return `Status Id ${status}`
      }


      
    }

}