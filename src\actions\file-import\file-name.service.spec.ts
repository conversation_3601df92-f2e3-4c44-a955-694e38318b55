// npx jest --config jest.config.js src/actions/file-import/file-name.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { FileNameService } from './file-name.service';
import { JobTypeEnum } from 'src/database/file-drop/entities/enums';


describe('FileNameService', () => {
  let service: FileNameService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FileNameService],
    }).compile();

    service = module.get<FileNameService>(FileNameService);
  });

  it('should generate contact import file name correctly', () => {
    const result = service.generateContactImportFileName('aud123', 'output.csv', true, true, 'user456');
    expect(result).toMatch(/^\d+_IW_aud123_user456_output\.csv$/);
  });

  it('should read contact import file name correctly', () => {
    const fileName = '1623456789_IW_aud123_user456_output.csv';
    const result = service.readContactImportFileName(fileName);
    expect(result).toEqual({
      audience_id: 'aud123',
      outputFileName: 'output.csv',
      importIntoFlex: true,
      washData: true,
      jobType: JobTypeEnum.WashAndFlexContactImport,
      userId: 'user456',
      companyId: '',
    });
  });

  it('should generate campaign file name correctly', () => {
    const result = service.generagenerateCampaignFileNameteFileName(
      '2025-05-15',
      true,
      true,
      '1623456789',
      'templateGroup',
      '30',
      'channel123',
      'output.csv'
    );
    expect(result).toBe('2025-05-15_IW_1623456789_templateGroup_30_channel123.csv_output.csv');
  });

  it('should read campaign import file name correctly', () => {
    const fileName = '14052025_SIW_1747313445_may25baby_30_12346000.csv_output.csv';
    const result = service.readCampaignImportFileName(fileName, 'user456', 'company789');

    console.log('Parsed result:', result);

    expect(result).toEqual({
      importIntoFlex: true,
      washData: true,
      scheduleDate: new Date(1747313445000),
      templateGroup: 'may25baby',
      cutOffMinutes: '30',
      channelId: '12346000',
      outputFileName: 'output.csv',
      jobType: JobTypeEnum.ContactDataWashingAndCampaignImporter,
      userId: 'user456',
      companyId: 'company789',
    });
  });

  it('should validate file format correctly', () => {
    const validFileName = '2025-05-15_IW_1623456789_templateGroup_30_channel123.csv_output.csv';
    const invalidFileName = 'invalid_file_name.csv';

    expect(service.validateFileFormat(validFileName)).toEqual({
      isValid: true,
      message: 'File format is valid.',
    });

    expect(service.validateFileFormat(invalidFileName)).toEqual({
      isValid: false,
      message: 'File format is invalid. Please follow the suggested format.',
    });
  });
});