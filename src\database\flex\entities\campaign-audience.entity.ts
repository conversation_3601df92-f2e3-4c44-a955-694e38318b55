import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

import { Campaign } from './campaigns.entity';

@Entity('campaign_audience')
export class CampaignAudience {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'campaign_id' })
  campaignId: string;

  @Column('bigint', { name: 'audience_id' })
  audienceId: string;

  @Column('bigint', { name: 'contact_id' })
  contactId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date | null;
  
  /*
  @ManyToOne(() => Campaign, (campaign) => campaign.campaignAudiences, { eager: true })
  @JoinColumn({ name: 'campaign_id' })
  campaign: Campaign;


  @ManyToOne(() => Audience, (audience) => audience.campaignAudiences, {
    nullable: true,
    eager: true,
  })
  @JoinColumn({ name: 'audience_id' })
  audience: Audience;
  

  @ManyToOne(() => Contacts, (contact) => contact.campaignAudiences, {
    nullable: true,
    eager: true,
  })
  @JoinColumn({ name: 'contact_id' })
  contact: Contacts;
  */
}
