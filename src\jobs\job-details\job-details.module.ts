import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobDetailsController } from './job-details.controller';
import { JobDetailsService } from './job-details.service';
import { JobDetail } from '../../database/file-drop/entities/job-detail.entity';
import { JobFilesModule } from '../job-files/job-files.module';
import { JobFiles } from 'src/database/file-drop/entities/job-files.entity';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { CompanyConfig } from 'src/database/file-drop/entities/company-config.entity';
import { CompanyConfigModule } from '../company-config/company-config.module';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';

@Module({
  imports: [TypeOrmModule.forFeature([JobDetail,JobFiles,CompanyConfig]), JobFilesModule, CompanyConfigModule],
  controllers: [JobDetailsController],
  providers: [JobDetailsService,GcpBucketService,CsvStreamProcessor],
  exports: [JobDetailsService],
})
export class JobDetailsModule {}