import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Job } from './job.entity';

@Entity('job_files')
export class JobFiles {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'bigint' })
  job_id: string;

  @Column({ type: 'bigint' })
  file_type_id: string;

  @Column({ type: 'varchar', length: 5000 })
  url: string;

  @Column({ type: 'text' })
  name: string;

  @Column({ type: 'bigint' })
  created_by_user_id: string;

  @Column({ type: 'bigint' })
  updated_by_user_id: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @ManyToOne(() => Job, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;
}