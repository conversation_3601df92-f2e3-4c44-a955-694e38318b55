import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFetchActiveJobs1746887951091 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP PROCEDURE IF EXISTS job_actions_lock_eligible;
        `);

        await queryRunner.query(`
            CREATE PROCEDURE job_actions_lock_and_fetch_ready(IN cool_down_seconds INT, IN lock_minutes INT)
            BEGIN
                DECLARE exit handler for SQLEXCEPTION
                BEGIN
                -- Rollback on error
                ROLLBACK;
                END;

                -- Create temp table
				DROP TEMPORARY TABLE IF EXISTS temp_job_actions;               
                CREATE TEMPORARY TABLE temp_job_actions (
                job_id BIGINT,
                id BIGINT,
                \`data\` JSON,
                \`action\` VARCHAR(255),
                job_action_type_id BIGINT,
                \`auth_token\` VARCHAR(5000)
                );

                START TRANSACTION;

                -- Insert eligible rows into temp table
                INSERT INTO temp_job_actions (job_id, id, \`data\`, \`action\`, \`job_action_type_id\`, \`auth_token\`)
                SELECT job_action.job_id, job_action.id, job_action.\`data\`, job_action.\`action\`, job_action.\`job_action_type_id\`, job.\`auth_token\`
                FROM job_action 
                INNER JOIN job ON job_action.job_id = job.id
                WHERE (job_action.status_id = 2 AND job_action.updated_at < DATE_SUB(NOW(), INTERVAL cool_down_seconds SECOND))
                    OR (job_action.status_id = 3 AND job_action.locked_until < NOW());

                -- Lock the selected rows in job_action
                UPDATE job_action
                SET
                locked_until = DATE_ADD(NOW(), INTERVAL lock_minutes MINUTE)
                WHERE id IN (SELECT id FROM temp_job_actions);

                COMMIT;

                -- Return selected rows
                SELECT * FROM temp_job_actions;

            END;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    }

}
