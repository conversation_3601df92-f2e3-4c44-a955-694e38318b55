import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Channels } from '../entities/channels.entity';

@Injectable()
export class ChannelService {

  private readonly channelRepository: Repository<Channels>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.channelRepository = this.flexDataSource.getRepository(Channels);
  }

  async findOneByHelpDeskId(helpdeskChannelId: string): Promise<Channels | null> {
    return this.channelRepository.findOne({ where: { helpdeskChannelId : helpdeskChannelId} });
  }
}