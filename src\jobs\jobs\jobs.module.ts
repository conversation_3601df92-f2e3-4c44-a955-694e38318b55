import { Modu<PERSON> } from '@nestjs/common';
import { JobService } from './jobs.service';
import { ContactWasherApiCompletedFilesModule } from '../contact-washer-api-completed-files/contact-washer-api-completed-files.module';
import { JobController } from './jobs.controller';
import { JwtService } from '@nestjs/jwt';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';
import { AudienceService } from '../../database/flex/services/audience.service';

@Module({
  imports: [
    DatabaseModule,

    ContactWasherApiCompletedFilesModule,
  ],
  controllers: [JobController],
  providers: [
    JobService,
    JwtService,
  ],
  exports: [JobService],
})
export class JobsModule {}