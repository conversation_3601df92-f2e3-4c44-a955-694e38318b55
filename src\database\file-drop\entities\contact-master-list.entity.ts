import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { WhatsappStatus } from './whatsapp-status.entity';
import { UpdateSource } from './update-source.entity';

@Entity('contact_master_list')
export class ContactMasterList {
  @PrimaryColumn({ type: 'varchar', length: 255 })
  phone_number: string;

  @Column({ type: 'bigint' })
  whatsapp_status_id: string;

  @Column({ type: 'datetime' })
  whatsapp_status_date: Date;

  @Column({ type: 'bigint' })
  update_source_id: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @ManyToOne(() => WhatsappStatus, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'whatsapp_status_id' })
  whatsappStatus: WhatsappStatus;

  @ManyToOne(() => UpdateSource, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'update_source_id' })
  updateSource: UpdateSource;
}