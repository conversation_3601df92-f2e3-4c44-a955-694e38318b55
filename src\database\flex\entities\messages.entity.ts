import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Campaign } from './campaigns.entity';


// import { CompanyBalanceLogs } from './company-balance-logs.entity.js';

@Entity('messages')
export class Messages {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column({ type: 'bigint', name: 'campaign_id', unsigned: true })
  campaignId: string;

  @Column({ type: 'bigint', name: 'channel_id', unsigned: true })
  channelId: string;
  
  @Column({ type: 'bigint', name: 'message_state_id', unsigned: true })
  messageStateId: string;
  
  @Column({ type: 'varchar', name: 'contact_number', unsigned: true })
  contactNumber: string;
  
  @Column({ type: 'bigint', name: 'contact_id', unsigned: true })
  contactId: string;

  @Column({ type: 'bigint', name: 'message_type_id', unsigned: true })
  messageTypeId: string;
  
  @Column({ type: 'bigint', name: 'channel_type_id', unsigned: true })
  channelTypeId: string;
  
  @Column({type:'text',name: 'header_params', nullable: true})
  headerParams: string | null;
  
  @Column({type:'text', name: 'body_params', nullable: true })
  bodyParams: string | null;

  @Column({type:'text', name: 'button_params', nullable: true })
  buttonParams: string | null;


  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column('varchar', { name: 'mb_message_id', nullable: true, length: 50 })
  mbMessageId: string | null;

  @Column('datetime', { name: 'sent_at', nullable: true })
  sentAt: Date | null;
  
  @Column('datetime', { name: 'scheduled_for', nullable: true })
  scheduledFor: Date | null;

  @Column('datetime', { name: 'delivered_at', nullable: true })
  deliveredAt: Date | null;

  @Column('varchar', { name: 'response', nullable: true })
  response: string | null;

  /*
  @OneToOne(() => CompanyBalanceLogs, (companyBalanceLogs) => companyBalanceLogs.campaign)
  companyBalanceLogs: CompanyBalanceLogs;

  @OneToOne(() => Channels)
  @JoinColumn({ name: 'channel_id', referencedColumnName: 'id' })
  channel: Channels | null;

  @ManyToOne(() => Campaign)
  @JoinColumn({ name: 'campaign_id', referencedColumnName: 'id' })
  campaign: Campaign;

  @ManyToOne(() => MessageStates)
  @JoinColumn({ name: 'message_state_id', referencedColumnName: 'id' })
  messageState: MessageStates;

  @ManyToOne(() => MessageType)
  @JoinColumn({ name: 'message_type_id', referencedColumnName: 'id' })
  messageType: MessageType;

  @ManyToOne(() => ChannelTypes)
  @JoinColumn({ name: 'channel_type_id', referencedColumnName: 'id' })
  channelType: ChannelTypes;
*/
}
