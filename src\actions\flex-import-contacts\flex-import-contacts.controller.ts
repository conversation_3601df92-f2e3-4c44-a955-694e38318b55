import { <PERSON>, Post, Param } from '@nestjs/common';
import { FileTypeEnum, JobDetailStatus, JobSubStatusEnum, WhatsAppStatus } from 'src/database/file-drop/entities/enums';
import { ContactService as FlexContactService } from 'src/database/flex/services/contact.service';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import * as libphonenumber from 'google-libphonenumber';
import { CsvExporterService } from 'src/utils/csv-exporter.service';
import { JobDetail } from 'src/database/file-drop/entities/job-detail.entity';
import { getExportDetailForImport } from 'src/jobs/job-details/helper/status-description.helper.functions';
import { JobStatsService } from 'src/database/file-drop/services/job-stats.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import Bottleneck from 'bottleneck';

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
const PhoneNumberType = libphonenumber.PhoneNumberType;
@Controller('actions/:actionId/flex-import-contacts')
export class FlexImportContactsController {

constructor(
        private readonly actionProcessingService: ActionProcessingService,
        private readonly flexContactService: FlexContactService,
        private readonly jobDetailsService: JobDetailsService,
        private readonly csvExporter: CsvExporterService,
        private readonly jobsStatsService: JobStatsService,
        private readonly jobService: JobService,
        private readonly jobfilesService: JobFilesService,
  ) {}

  @Post('generate-result-files')
  async generateResultFiles(@Param('actionId') actionId: string): Promise<void> {
     await this.actionProcessingService.process(actionId, async (action) => {
        const fileName = `export/${action.job.company_id}/${action.job_id}_FlexImportErrors.csv`;

        await this.csvExporter.exportToCsv(
            fileName,
            async (lastIndex) => {
                //return this.jobDetailsService.findAllByJobIdBatched(action.job_id, 100, lastIndex, null, null);
                return await this.jobDetailsService.findAllWithoutContactIdByJobIdBatched(action.job_id, 100, lastIndex);
            },
            () => {
                const headers = action.job.file_headers.split(',');

                headers.push('Reason');

                return headers;
            },
            (row : any) => {
                    const jobDetailRow = row as JobDetail;
                    const output = jobDetailRow.row_data;
                    output['Reason'] = getExportDetailForImport(jobDetailRow.job_detail_status_id);

                    return output;
            });


            const failedFileName = 'Failed_' + action.job.original_file_name.split('_').pop();

            const file = await this.jobfilesService.create({ 
                job_id: action.job_id, name: fileName, 
                created_by_user_id: action.job.created_by_user_id, 
                updated_by_user_id: action.job.created_by_user_id, 
                file_type_id: 
                FileTypeEnum.FailedFlexImport.toString(), url:''});
            this.jobsStatsService.updateByJobId(action.job_id, {failed_file_url: '/files/' + file.id, failed_file_name: failedFileName });
    
        }, JobSubStatusEnum['Creating Results File']);


    } 

  @Post('')
  async importContacts(@Param('actionId') actionId: string): Promise<void> {
    const validStatusIds : string[] = [JobDetailStatus.Inserted.toString(), JobDetailStatus.PreProcessingSkippedDueToCutOffTime.toString(), JobDetailStatus.WashedViaApi.toString()]
    await this.actionProcessingService.process(actionId, async (action) => {
        
        let lastRowIndex = 0;
        let batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id,100, lastRowIndex, validStatusIds, null);
        let stats = await this.jobsStatsService.findOne(action.job_id);
        let percentageCompleteAtStart = action.job.percentage_complete;
      
        const limiter = new Bottleneck({ maxConcurrent: 10 });
    
        const audiencesId = action.data.audience_id;
        while (batchToProcess.length > 0) {

                await Promise.all(batchToProcess.map(jobDetail =>
                    limiter.schedule(async () => {


                        if (jobDetail.source_row_index > lastRowIndex) {
                            lastRowIndex = jobDetail.source_row_index;
                        }

                        if (jobDetail.contact_id){
                            return;
                        }

                        const parsedNumber = phoneUtil.parse('+' + jobDetail.sanitized_number);
                        const countryName = phoneUtil?.getRegionCodeForNumber(parsedNumber) ?? jobDetail.row_data["country"] ?? "";
                        const countryCode = parsedNumber?.getCountryCode() ?? "";
                        const isPossibleMobile = phoneUtil.isPossibleNumberForType(parsedNumber, PhoneNumberType.MOBILE);
                        const nationalNumber = parsedNumber.getNationalNumber().toString();
                        const isValidForRegion = phoneUtil.isValidNumberForRegion(parsedNumber, countryName);

                        // Strict length check for South Africa, otherwise trust libphonenumber
                        const isValidLength = countryName === 'ZA' ? nationalNumber.length === 9 : true;
                        const isValid = parsedNumber && countryCode && isValidForRegion && isPossibleMobile && isValidLength;

                        if (isValid) {
                            const result = await this.flexContactService.upsertContact(
                                BigInt(action.job.company_id),
                                jobDetail.sanitized_number,
                                BigInt(action.job.created_by_user_id),
                                jobDetail.row_data["first_name"],
                                jobDetail.row_data["surname"],
                                countryName,
                                countryCode,
                                1,
                                jobDetail.row_data["optin_status"],
                                jobDetail.job.original_file_name,
                                jobDetail.source_row_index.toString(),
                                JSON.stringify(jobDetail.row_data),
                                audiencesId);

                            jobDetail.job_detail_status_id = JobDetailStatus.Inserted.toString();
                            jobDetail.contact_id = result.contact_id;
                        } else {
                            jobDetail.job_detail_status_id = JobDetailStatus.InvalidNumber.toString();
                            console.info(`Invalid Number : ${jobDetail.sanitized_number} - Country: ${countryName} - Code: ${countryCode} - valid ${isValid} - Possible Mobile: ${isPossibleMobile} - National: ${nationalNumber}`);
                        }

                        await this.jobDetailsService.update(jobDetail.id, jobDetail);
            })));

                                if (stats.total_details){
                        const newPercentageComplete = Math.floor((lastRowIndex / stats.total_details) * action.percentage_of_job);

                        await this.jobService.update(action.job_id, {
                            percentage_complete: newPercentageComplete
                        });
                    }

            batchToProcess = await this.jobDetailsService.findAllByJobIdBatched(action.job_id,100, lastRowIndex, validStatusIds, null);
            console.log(`Loaded Contact batch lastIndex: ${lastRowIndex}`);
        }
    }, JobSubStatusEnum['Creating Contacts']); 
   
  }
}