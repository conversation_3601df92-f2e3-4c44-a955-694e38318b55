import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TemplateGroup } from '../entities/template-groups.entity';


@Injectable()
export class TemplateGroupsService {

  private readonly templateGroupsRepository: Repository<TemplateGroup>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.templateGroupsRepository = this.flexDataSource.getRepository(TemplateGroup);
  }

  async findOne(id: string, companyId: string): Promise<TemplateGroup | null> {
    return this.templateGroupsRepository.findOne({ where: { id, companyId }, relations: ["templates"] });
  }

  async findOneByName(name: string, companyId: string): Promise<TemplateGroup | null> {
    return this.templateGroupsRepository.findOne({ where: { name, companyId }, relations: ["templates"] });
  }
  
}