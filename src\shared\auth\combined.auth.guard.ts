import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { BasicAuthGuard } from './basic-auth.guard';

@Injectable()
export class CombinedAuthGuard implements CanActivate {
  constructor(
    private readonly jwtAuthGuard: JwtAuthGuard,
    private readonly basicAuthGuard: BasicAuthGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      return (await this.jwtAuthGuard.canActivate(context)) as boolean;
    } catch (e) {
      return (await this.basicAuthGuard.canActivate(context)) as boolean;
    }
  }
}
