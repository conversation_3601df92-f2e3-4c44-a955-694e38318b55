import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('channels', { schema: 'preprod_sweeshapi' })
export class Channels {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('varchar', { name: 'name', length: 191 })
  name: string;

  @Column('bigint', { name: 'company_id' })
  companyId: bigint;

  // phone renamed to channel_identified
  @Column('varchar', { name: 'channel_identifier', nullable: true, length: 255 })
  channelIdentifier: string | null;

  @Column('varchar', { name: 'namespace', nullable: true, length: 50 })
  namespace: string | null;

  @Column('int', { name: 'helpdesk_channel_id', nullable: true, unique: true })
  helpdeskChannelId: string | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column('tinyint', { name: 'enabled_auto_reply', width: 1, default: () => 0 })
  enabledAutoReply: boolean;

  @Column('tinyint', { name: 'status', width: 1, default: () => 0 })
  status: boolean;

  @Column('bigint', { name: 'window_opener_template_id', nullable: false })
  windowOpenerTemplateId: bigint;

  @Column('enum', {
    name: 'provider_type',
    nullable: true,
    enum: ['smooch', '360dialog', 'telegram', 'cloudapi'],
  })
  providerType: 'smooch' | '360dialog' | 'telegram' | 'cloudapi' | null;

  @Column('tinyint', { name: 'enabled_optout_reply', width: 1, default: () => 0 })
  enabledOptoutReply: boolean;

  @Column({ name: 'tier_limit', default: '1k' })
  tierLimit: string;

  @Column({ nullable: true })
  quality: string;

  @Column('varchar', { nullable: true })
  error: string | null;

  @Column('text', { nullable: true })
  channel_type: string | null;
  

/*
  @ManyToOne(() => Company, (company) => company.channels)
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Company;

  @ManyToOne(() => ChannelTypes, (channelTypes) => channelTypes.channels, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'channel_type' })
  channelType: ChannelTypes;


  @OneToMany(() => Campaign, (campaigns) => campaigns.campaignType)
  campaigns: Campaign[];

  @OneToMany(() => Templates, (templates) => templates.channel)
  templates: Templates[];

  @OneToMany(() => Bots, (bot) => bot.channel)
  bots: Bots[];


  @OneToOne(() => ChannelExternalCredentials, (credential) => credential.channel, {
    cascade: true,
  })
  externalCredential: ChannelExternalCredentials;

  @OneToOne(() => TrialChannel, (trialChannel) => trialChannel.channel)
  @JoinColumn({ name: 'trial_channel_id' })
  trialChannel: TrialChannel;
  */
}
