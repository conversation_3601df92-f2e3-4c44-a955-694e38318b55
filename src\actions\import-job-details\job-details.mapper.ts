import { JobDetailStatus, WhatsAppStatus } from 'src/database/file-drop/entities/enums';
import { JobDetail } from 'src/database/file-drop/entities/job-detail.entity';
import { CleanObjectUtil } from 'src/utils/clean-object.util';
import { PhoneNumberUtil } from 'src/utils/phone-number.util';

export class JobDetailsMapper {
  static createBatchEntry(jobId: string, row: any, rowIndex: number, mobileHeader: string, countryHeader: string, countryDefault: string): Partial<JobDetail> {
    CleanObjectUtil.cleanObject(row);
    const mobileNumber = row[mobileHeader]?.toString() ?? '';
    
    let { sanitizedNumber, isValid } = PhoneNumberUtil.validateAndSanitize(mobileNumber, row[countryHeader] ?? countryDefault);

    if (!sanitizedNumber)
    {
      sanitizedNumber = mobileNumber.replace(/[^\d+]/g, '');
    }

    // Ensure no + or other symbles are in the sanitized number
    sanitizedNumber = sanitizedNumber.replace(/[^0-9]/g, '');

    return  {
      job_id: jobId,
      phone_number: mobileNumber,
      sanitized_number: sanitizedNumber,
      row_data: row,
      source_row_index: rowIndex,
      whatsapp_status_id: BigInt(WhatsAppStatus.Unknown).toString(),
      job_detail_status_id: isValid ? BigInt(JobDetailStatus.Inserted).toString() : BigInt(JobDetailStatus.InvalidNumber).toString(),
    };
 }
}