export enum JobTypeEnum {
  Unknown = 0,
  ContactDataWashing = 1,
  ContactImporter = 2,
  CampaignImporter = 3,
  MasterListFileImport = 4,
  ContactDataWashingAndCampaignImporter = 5,
  ProcessJobByFileName = 6,
  FlexContactImport = 7,
  WashAndFlexContactImport = 8,
}

export enum JobStatus {
  Created = 1,
  FileUploaded = 2,
  Processing = 3,
  ProcessingOutput = 5,
  Ready = 6,
  Completed = 4,
  Errored = 100,
  Paused = 101,
  Stopped = 102,
}

export enum WhatsAppStatus {
  NotOnWhatsApp = 1,
  OnWhatsApp = 2,
  Unknown = 3,
  InvalidNumber = 4,
  ManualWashRequired = 5,
}

export enum JobDetailStatus {
  Inserted = 1,
  WashedViaDB = 2,
  WashedViaApi = 3,
  ManualWashRequired = 4,
  Duplicate = 5,
  InvalidNumber = 6,
  ApiWashRequired = 7,
  SentToApi = 8,
  Paused = 9,
  Stopped = 10,
  PreProcessingSkippedDueToCutOffTime = 11,
  FailedToImport = 12,
  Successful = 13,
  FailedToAddToCampaign = 14,
}

export enum FileTypeEnum {
  OrigonalFile = 1,
  FullWashedExport = 2,
  FailedFlexImport = 3,
  CampaignReportFile = 4,
}

export enum JobActionTypeEnum{
  ImportJobDetailsFromFile = 1,
  DetectDuplicates = 2,
  WashWahatsAppNumbersData = 3,
  ImportContactsIntoFlex = 4,
  GenerateImportResultFiles = 5,
  ExportWashWahatsAppNumbersData=6,
  CreateFlexCampaign =7, 
  UpdateCampaignResults=8,
  ExportCampaignResults=9,
  FlexWebHookOptoutUpdates=11,
}

export enum JobActionStatusEnum{
  Created = 1,
  Ready = 2,
  Processing = 3,
  Completed = 4,
  QueuedForProcessing = 5,
  Errored = 100,
  Paused = 101,
  Stopped = 102,
}

export enum JobSubStatusEnum{
  Initilizing = 1,
  'Loading Data' = 2,
  'Validating Data' = 3,
  'Validating Number' = 4,
  'Creating Contacts' = 5,
  'Creating Results File' = 6,
  'Creating Campaign' = 7,
  'Mobile Number Header not found in file' = 8,
  InvalidScheduleDate = 9,
  'Sending Webhook Data' = 10
}