import { Injectable } from '@nestjs/common';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { ConfigService } from '@nestjs/config';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { JobDetail } from 'src/database/file-drop/entities/job-detail.entity';
import { JobDetailStatus, WhatsAppStatus } from 'src/database/file-drop/entities/enums';
import { stringify } from 'csv-stringify';
import { MemoryUtil } from 'src/utils/memory.util';

@Injectable()
export class FullWashedDataService {
  constructor(
    private readonly jobService : JobService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly jobDetailsService: JobDetailsService,
    private readonly config: ConfigService
  ) {}

  async createFullWashedDataExportFile(jobId: string): Promise<void> {
    debugger;
    console.log(`Creating Full Washed Data Export File for Job ID: ${jobId}`);

    const job = await this.jobService.findOneWithoutCompanyCheck(jobId); // Replace 'companyIdPlaceholder' with the actual company ID
    //const origonalFile = await this.
    const bucketName = this.config.get('GCP_BUCKET_NAME');
    const fileName = `export/${job.company_id}/${job.original_file_name.replace('.csv','')}_${jobId}.csv`;

    const writableStream = await this.gcpBucketService.getWritableStream(bucketName, fileName);

    const batchSize = 100;
    
    let lastRowIndex = 0;
    let jobDetails = await this.jobDetailsService.findAllByJobIdBatched(jobId, batchSize, lastRowIndex, [], []);
    let headers = job.file_headers.split(',');
    headers.push('IsOnWhatsapp');
    headers.push('ExportDetail');

    const csvStringifier = stringify({ header: true, columns: headers });
    csvStringifier.pipe(writableStream);
    let repeatCount = 0;

    while (jobDetails && jobDetails.length > 0) {

        for (const detail of jobDetails) {
            const rowData = detail.row_data;
            rowData['IsOnWhatsapp'] = this.hasWhats(detail);
            rowData['ExportDetail'] = this.exportDetail(detail);

            const row = headers.map((header) => rowData[header] || '');
            debugger;
            csvStringifier.write(row);

            if (lastRowIndex < detail.source_row_index) {
                lastRowIndex = detail.source_row_index;
            }
            else{
                repeatCount++;
            }
        }

        await MemoryUtil.pauseIfHighMemory();

        console.log(`Fetching next batch of job details... Last Row Index: ${lastRowIndex}`);
        jobDetails = await this.jobDetailsService.findAllByJobIdBatched(jobId, batchSize, lastRowIndex, [], []);

        if (repeatCount > 3) {
            console.error(`Repeat Safeguard trigged.`);
            break;
        }
    }

    await new Promise<void>((resolve, reject) => {
      writableStream.on('finish', resolve);
      writableStream.on('error', reject);
      csvStringifier.end(); // Ensure the CSV stringifier ends properly
    });

    console.log(`Writing Complete : Last Index ${lastRowIndex}`);
  }

  private hasWhats(input : JobDetail) : string {
    if (input.whatsapp_status_id === WhatsAppStatus.OnWhatsApp.toString()) {
      return 'Yes';
    } else if (input.whatsapp_status_id ===  WhatsAppStatus.NotOnWhatsApp.toString()) {
      return 'No';
    } else {
      return 'Unknown';
    }
  }

  private exportDetail(input : JobDetail) : string {
    if (input.job_detail_status_id === JobDetailStatus.Duplicate.toString()) {
      return 'Duplicate';
    } else if (input.whatsapp_status_id ===  JobDetailStatus.InvalidNumber.toString()) {
      return 'Invalid Number';
    } else if (input.whatsapp_status_id === WhatsAppStatus.OnWhatsApp.toString()) {
        return 'On Whats App';
    }else if (input.whatsapp_status_id === WhatsAppStatus.NotOnWhatsApp.toString()) {
      return 'Not on Whats App';
    }
    else {
      return input.system_message;
    }
  }
}