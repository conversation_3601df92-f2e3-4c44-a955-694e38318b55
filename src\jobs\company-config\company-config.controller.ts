import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Post, Put, Delete, Body, Param } from '@nestjs/common';
import { CompanyConfigService } from './company-config.service';
import { CompanyConfig } from '../../database/file-drop/entities/company-config.entity';

@ApiTags('Company Config')
@Controller('company-config')
export class CompanyConfigController {
  constructor(private readonly companyConfigService: CompanyConfigService) {}

  @Post()
  async create(@Body() data: Partial<CompanyConfig>): Promise<CompanyConfig> {
    return this.companyConfigService.create(data);
  }

  @Get()
  async findAll(@Param('id') id: string): Promise<CompanyConfig[]> {
    return this.companyConfigService.findAll(id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<CompanyConfig | null> {
    return this.companyConfigService.findOne(id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<CompanyConfig>,
  ): Promise<CompanyConfig> {
    return this.companyConfigService.update(id, data);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    return this.companyConfigService.delete(id);
  }
}