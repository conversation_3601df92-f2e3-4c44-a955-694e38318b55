import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPercentageOfJobToActionTable1747156503965 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action ADD COLUMN percentage_of_job int`);
        await queryRunner.query(`ALTER TABLE job ADD COLUMN percentage_complete int`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN percentage_of_job`);
        await queryRunner.query(`ALTER TABLE job DROP COLUMN percentage_complete`);
    }

}
