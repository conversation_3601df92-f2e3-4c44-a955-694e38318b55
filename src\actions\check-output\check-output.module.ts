import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { CheckOutputService } from './check-output.service';
import { JobService } from 'src/jobs/jobs/jobs.service';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Job } from 'src/database/file-drop/entities/job.entity';
import { JobFiles } from 'src/database/file-drop/entities/job-files.entity';
import { CheckOutputController } from './check-output.controller';

@Module({
  imports: [
    JobFilesModule,
    TypeOrmModule.forFeature([Job, JobFiles]),
  ],
  providers: [CheckOutputService, JobService, GcpBucketService, JobFilesService],
  controllers: [CheckOutputController],
  exports: [CheckOutputService],
})
export class CheckOutputModule {}