import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { JobFilesService } from './job-files.service';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';

@ApiTags('Job Files')
@Controller('jobs/job-files')
export class JobFilesController {
  constructor(private readonly jobFilesService: JobFilesService) {}

  @Get()
  async findAll(): Promise<JobFiles[]> {
    return this.jobFilesService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<JobFiles | null> {
    return this.jobFilesService.findOne(id);
  }

  @Post()
  async create(@Body() jobFile: JobFiles): Promise<JobFiles> {
    return this.jobFilesService.create(jobFile);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() jobFile: Partial<JobFiles>): Promise<JobFiles> {
    await this.jobFilesService.update(id, jobFile);
    return this.jobFilesService.findOne(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    await this.jobFilesService.remove(id);
  }

  @Get(':job_id/files')
async getFilesForJob(@Param('job_id') jobId: string): Promise<JobFiles[]> {
  return this.jobFilesService.getFilesForJob(jobId);
}
}