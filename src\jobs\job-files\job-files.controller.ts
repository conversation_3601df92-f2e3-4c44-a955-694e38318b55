import { Controller, Get, Post, Put, Delete, Param, Body, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { JobFilesService } from './job-files.service';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';
import { GcpBucketService } from '../../utils/gcp-bucket.service';
import { ConfigService } from '@nestjs/config';

@ApiTags('Job Files')
@Controller('jobs/job-files')
export class JobFilesController {
  constructor(
    private readonly jobFilesService: JobFilesService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService
  ) {}

  @Get()
  async findAll(): Promise<JobFiles[]> {
    return this.jobFilesService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<JobFiles | null> {
    return this.jobFilesService.findOne(id);
  }

  @Post()
  async create(@Body() jobFile: JobFiles): Promise<JobFiles> {
    return this.jobFilesService.create(jobFile);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() jobFile: Partial<JobFiles>): Promise<JobFiles> {
    await this.jobFilesService.update(id, jobFile);
    return this.jobFilesService.findOne(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    await this.jobFilesService.remove(id);
  }

  @Get(':job_id/files')
  async getFilesForJob(@Param('job_id') jobId: string): Promise<JobFiles[]> {
    return this.jobFilesService.getFilesForJob(jobId);
  }

  @Get('download/:id')
  async downloadFile(
    @Param('id') fileId: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const file = await this.jobFilesService.findOne(fileId);

      if (!file) {
        res.status(404).send('File not found');
        return;
      }

      const bucketName = this.config.get('GCP_BUCKET_NAME');
      const fileStream = await this.gcpBucketService.getFileStream(bucketName, file.name);

      res.setHeader('Content-Disposition', `attachment; filename="${file.name}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      fileStream.pipe(res);
    } catch (error) {
      console.error('Error downloading file:', error);
      res.status(500).send('Error retrieving file');
    }
  }
}