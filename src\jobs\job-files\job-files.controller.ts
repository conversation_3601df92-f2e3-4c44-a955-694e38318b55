import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Res,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { Readable } from 'stream';
import { JobFilesService } from './job-files.service';
import { JobFiles } from '../../database/file-drop/entities/job-files.entity';
import { ConfigService } from '@nestjs/config';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';

@ApiTags('Job Files')
@Controller('jobs/job-files')
export class JobFilesController {
  constructor(
    private readonly jobFilesService: JobFilesService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService,
  ) {}

  @Get()
  async findAll(): Promise<JobFiles[]> {
    return this.jobFilesService.findAll();
  }

  @Get('download/:id')
  async downloadFile(
    @Param('id') fileId: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const file = await this.jobFilesService.findOne(fileId);

      if (!file) {
        res.status(404).send('File not found');
        return;
      }

      const bucketName = this.config.get('GCP_BUCKET_NAME');

      // Try to get the file stream using the name as stored
      let fileStream: Readable;
      let fileName = file.name;

      try {
        // First try with the name as is (might be full path)
        fileStream = await this.gcpBucketService.getFileStream(
          bucketName,
          file.name,
        );
      } catch (error) {
        console.log(`File not found with name: ${file.name}, trying with URL...`);

        // If that fails and we have a URL, try to extract the path from URL
        if (file.url) {
          try {
            const url = new URL(file.url);
            const pathFromUrl = decodeURIComponent(url.pathname.slice(1)); // Remove leading slash
            console.log(`Trying with path from URL: ${pathFromUrl}`);
            fileStream = await this.gcpBucketService.getFileStream(
              bucketName,
              pathFromUrl,
            );
            fileName = pathFromUrl.split('/').pop() || file.name; // Get just the filename for download
          } catch (urlError) {
            console.error('Error extracting path from URL:', urlError);
            throw error; // Throw the original error
          }
        } else {
          throw error; // Throw the original error if no URL available
        }
      }

      // Extract just the filename for the download header
      const downloadFileName = fileName.split('/').pop() || fileName;

      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${downloadFileName}"`,
      );
      res.setHeader('Content-Type', 'application/octet-stream');

      fileStream.pipe(res);
    } catch (error) {
      console.error('Error downloading file:', error);
      res.status(500).send('Error retrieving file');
    }
  }

  @Get(':job_id/files')
  async getFilesForJob(@Param('job_id') jobId: string): Promise<JobFiles[]> {
    return this.jobFilesService.getFilesForJob(jobId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<JobFiles | null> {
    return this.jobFilesService.findOne(id);
  }

  @Post()
  async create(@Body() jobFile: JobFiles): Promise<JobFiles> {
    return this.jobFilesService.create(jobFile);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() jobFile: Partial<JobFiles>,
  ): Promise<JobFiles> {
    await this.jobFilesService.update(id, jobFile);
    return this.jobFilesService.findOne(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    await this.jobFilesService.remove(id);
  }
}
