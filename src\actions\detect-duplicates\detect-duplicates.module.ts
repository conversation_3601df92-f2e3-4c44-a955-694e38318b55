import { Module } from '@nestjs/common';
import { DetectDuplicatesService } from './detect-duplicates.service';
import { DetectDuplicatesController } from './detect-duplicates.controller';
import { JobDetailsService } from '../../jobs/job-details/job-details.service';
import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { EngineModule } from 'src/engine/engine.module';
import { ActionProcessingService } from 'src/engine/action-processing.service';

@Module({
  imports: [DatabaseModule, EngineModule],
  controllers: [DetectDuplicatesController],
  providers: [DetectDuplicatesService, JobDetailsService, ActionProcessingService],
})
export class DetectDuplicatesModule {}