import { Injectable } from '@nestjs/common';
import { JobTypeEnum } from 'src/database/file-drop/entities/enums';
import { FileImportWorkingDataDto } from './dtos/file-import-working-data.dto';
import { FileImportCampaignWorkingDataDto } from './dtos/file-import-campaign-working-data.dto';
import { channel } from 'diagnostics_channel';

@Injectable()
export class FileNameService {
  generateContactImportFileName(audience_id: string, outputFileName: string,importIntoFlex: boolean, WashData: boolean, userId: string): string {
      
      let actions = '';

      if (importIntoFlex) {
          actions += 'I';
      }

      if (WashData) {
          actions += 'W';
      }

      return `${Date.now()}_${actions}_${audience_id}_${userId}_${outputFileName}`;
  }

  readContactImportFileName(fileName: string):FileImportWorkingDataDto{
      const fileSections = fileName.split('_');

      const output = {
          audience_id: fileSections[2],
          outputFileName: fileSections[4],
          importIntoFlex: fileSections[1].includes('I'),
          washData: fileSections[1].includes('W'),
          jobType: null as JobTypeEnum,
          userId: fileSections[3],
          companyId: '',
      }

      if (output.washData && output.importIntoFlex) {
          output.jobType = JobTypeEnum.WashAndFlexContactImport;
      }
      else if (output.washData) {
          output.jobType = JobTypeEnum.ContactDataWashing;
      } else if (output.importIntoFlex) {
          output.jobType = JobTypeEnum.FlexContactImport;
      } else {
          output.jobType = JobTypeEnum.Unknown;;
      }

      return output;
  }

  generagenerateCampaignFileNameteFileName(date: string, importIntoFlex: boolean, WashData: boolean, scheduledEpochDate: string, templateGroup: string, cutOffMinutes: string, channelId: string, outputFileName: string): string {
    let actions = '';

    if (!date){
      date = new Date().toISOString();
    }

      if (importIntoFlex) {
          actions += 'I';
      }

      if (WashData) {
          actions += 'W';
      }

      if (outputFileName)
        outputFileName = '_' + outputFileName;

      if (!cutOffMinutes)
        cutOffMinutes = '';
    
    
    return `${date}_${actions}_${scheduledEpochDate}_${templateGroup}_${cutOffMinutes}_${channelId}.csv${outputFileName}`;
  }

  readCampaignImportFileName(fileName: string, userId: string, companyId: string):FileImportCampaignWorkingDataDto{
    const fileSections = fileName.split('_');

    // Ensure correct length
    const scheduleDateString = fileSections[2].padEnd(13, '0');

    const output = {
      importIntoFlex: fileSections[1].includes('I'),
      washData: fileSections[1].includes('W'),
      scheduleDate: new Date(Number.parseInt(scheduleDateString)),
      templateGroup: fileSections[3],
      cutOffMinutes: fileSections[4],
      channelId: fileSections[5].split('.')[0],
      outputFileName: fileSections[6],
      jobType:JobTypeEnum.CampaignImporter,
      userId: userId,
      companyId: companyId,
    }

    if (output.washData && output.importIntoFlex) {
        output.jobType = JobTypeEnum.ContactDataWashingAndCampaignImporter;
    }

    return output;
}

    validateFileFormat(fileName: string) { 
      // Regular expression to validate the file name format
      const regex = /^\d{4}-\d{2}-\d{2}_[a-zA-Z]+_\d+_[a-zA-Z]+_\d+_[a-zA-Z0-9]+\.csv\.[a-zA-Z0-9]+$/;
  
      if (regex.test(fileName)) {
        return { isValid: true, message: 'File format is valid.' };
      } else {
        return { isValid: false, message: 'File format is invalid. Please follow the suggested format.' };
      }
    }
}