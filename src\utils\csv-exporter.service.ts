import { Injectable } from '@nestjs/common';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { ConfigService } from '@nestjs/config';
import { stringify } from 'csv-stringify';
import { MemoryUtil } from 'src/utils/memory.util';

@Injectable()
export class CsvExporterService {
  constructor(
    private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService
  ) {}

  async exportToCsv(fileName: string, fetchData: (lastIndex) => Promise<any>, getHeaders: ()=>string[], createOutputObject: (row: any)=> any ): Promise<void> {
    const bucketName = this.config.get('GCP_BUCKET_NAME');

    const writableStream = await this.gcpBucketService.getWritableStream(bucketName, fileName);
  
    console.log(`Writing to GCP bucket: ${bucketName}, file: ${fileName}`);

    let lastRowIndex = 0;
    let jobDetails = await fetchData(lastRowIndex);


    const csvStringifier = stringify({ header: true, columns: getHeaders() });
    csvStringifier.pipe(writableStream);
    let repeatCount = 0;

    while (jobDetails && jobDetails.length > 0) {

        for (const detail of jobDetails) {
            const row = createOutputObject(detail);
            csvStringifier.write(row);

            if (lastRowIndex < detail.source_row_index) {
                lastRowIndex = detail.source_row_index;
            }
            else{
                repeatCount++;
            }
        }

        await MemoryUtil.pauseIfHighMemory();

        console.log(`Fetching next batch of job details... Last Row Index: ${lastRowIndex}`);
        jobDetails = await fetchData(lastRowIndex);

        if (repeatCount > 3) {
            console.error(`Repeat Safeguard trigged.`);
            break;
        }
    }

    await new Promise<void>((resolve, reject) => {
      writableStream.on('finish', resolve);
      writableStream.on('error', reject);
      csvStringifier.end(); // Ensure the CSV stringifier ends properly
    });

    console.log(`Writing Complete : Last Index ${lastRowIndex}`);
  }
}