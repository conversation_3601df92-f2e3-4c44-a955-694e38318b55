# File Drop Jobs

## Description
A NestJS backend for handling file drop jobs, including file import, processing, and reporting.

## Prerequisites
- Node.js 18+
- Docker and Docker Compose

## Installation
```bash
npm install
```

## Running the app
### Local Development
```bash
npm run start:dev
```

### Docker
```bash
docker-compose up --build
```

## Test
```bash
npm run test
```

## Deployment
### Google Cloud Run
1. Build the Docker image:
   ```bash
   docker build -t gcr.io/<PROJECT_ID>/file-drop-jobs .
   ```
2. Push the image to Google Container Registry:
   ```bash
   docker push gcr.io/<PROJECT_ID>/file-drop-jobs
   ```
3. Deploy to Cloud Run:
   ```bash
   gcloud run deploy file-drop-jobs \
     --image gcr.io/<PROJECT_ID>/file-drop-jobs \
     --platform managed \
     --region <REGION> \
     --allow-unauthenticated
   ```


## GCP Setup
gcloud config set project flex-dev-444304
gsutil notification create -t FileDropEvent -f json gs://flex-dev-file-drop


gcloud config set project flex-preprod
gsutil notification create -t FileDropEvent -f json gs://preprod-flex-filedrop
gsutil cors set cors-config.json gs://preprod-flex-filedrop


gsutil cors set cors-config.json gs://your-bucket-name
gsutil cors set cors-config.json gs://flex-dev-file-drop

curl -i -X OPTIONS https://storage.googleapis.com/flex-dev-file-drop/contact/20/1746710335476_IW_5_18_sample.csv?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=************-compute%40developer.gserviceaccount.com%2F20250508%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250508T131855Z&X-Goog-Expires=900&X-Goog-SignedHeaders=host&X-Goog-Signature=bcb309787e7c69a0ed88b5ef55b5ef45478e4545a79736f5c68cb7ed3c0ce9b6ceb4f076ee197e35313f2fc92da39f58ac8748fc0c7869e62ab8decc55a0172c35b7c4fa0765eb78a61c4339e19ebd68a3d8471be919c31fe7d5e92ad99a4f53fd4ee84de7bf953cf228b3dedfae8956e07f637dbcaa82e32e6c90c71b360d4efa89eea30c7912850f14d7379b50349959ead5c434c7fe9f5aa4f497f5c556cf08e4dd2fa799b952763c21cf26c8dde379487d4cbf0fb259b4c100f110aea37f98dfa467829fff05896348f6a440d652776967d933e61187f344267d5a3b3f237c8a797a23fbff5e813f68eb71a205507f6b530d47241648b4d79a628c6e77fb


npx typeorm migration:create src/database/file-drop/migrations/AddJobActionLocks

C:\ChatInc\Solutions\Flex\flex-file-drop\file-drop-jobs\bucket-function>gcloud storage buckets notifications delete gs://preprod-flex-filedrop