import { MigrationInterface, QueryRunner } from "typeorm";

export class JobStatsUpdate1747164676636 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
          await queryRunner.query(`DROP PROCEDURE IF EXISTS update_job_stats`);
          
          await queryRunner.query(`DROP PROCEDURE IF EXISTS job_stats_update`);

          await queryRunner.query(`
CREATE PROCEDURE job_stats_update(IN in_job_id BIGINT)
BEGIN
INSERT INTO job_stats (
  job_id,
  last_updated,
  total_details,
  total_complete,
  total_unprocessed,
  total_manualWashRequired,
  total_apiWashRequired,
  total_SentToApi,
  total_washedViaDB,
  total_washedViaApi,
  total_duplicate,
  total_invalidNumber,
  total_Paused,
  total_Stopped,
  total_PreProcessingSkippedDueToCutOffTime,
  progress
)
SELECT
  jd.job_id,
  NOW(),
  COUNT(*) AS total_details,
  COUNT(CASE WHEN jds.is_complete = TRUE THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 1 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 4 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 7 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 8 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 2 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 3 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 5 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 6 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 9 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 10 THEN 1 ELSE NULL END),
  COUNT(CASE WHEN jd.job_detail_status_id = 11 THEN 1 ELSE NULL END),
  CASE
    WHEN COUNT(*) = 0 THEN 0
    ELSE (COUNT(CASE WHEN jds.is_complete = TRUE THEN 1 ELSE NULL END) / COUNT(*) * 100)
  END
FROM job_detail jd
INNER JOIN job_detail_status jds ON jds.id = jd.job_detail_status_id
INNER JOIN job j ON j.id = jd.job_id AND j.created_at >= NOW() - INTERVAL 14 DAY
LEFT JOIN job_stats js ON js.job_id = j.id
WHERE (
      in_job_id IS NULL
      AND (j.updated_at > js.last_updated OR js.last_updated IS NULL OR j.status_id = 3)
    )
    OR j.id = in_job_id
GROUP BY jd.job_id
ON DUPLICATE KEY UPDATE
  last_updated = NOW(),
  total_details = VALUES(total_details),
  total_complete = VALUES(total_complete),
  total_unprocessed = VALUES(total_unprocessed),
  total_manualWashRequired = VALUES(total_manualWashRequired),
  total_apiWashRequired = VALUES(total_apiWashRequired),
  total_SentToApi = VALUES(total_SentToApi),
  total_washedViaDB = VALUES(total_washedViaDB),
  total_washedViaApi = VALUES(total_washedViaApi),
  total_duplicate = VALUES(total_duplicate),
  total_invalidNumber = VALUES(total_invalidNumber),
  total_Paused = VALUES(total_Paused),
  total_Stopped = VALUES(total_Stopped),
  total_PreProcessingSkippedDueToCutOffTime = VALUES(total_PreProcessingSkippedDueToCutOffTime),
  progress = VALUES(progress);

END; `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP PROCEDURE IF EXISTS job_stats_update`);
    }

}
