import { <PERSON>, Get, Param, HttpException, HttpStatus, Post, Body, UploadedFile, UseInterceptors, Query, UseGuards, Req } from '@nestjs/common';
import { GetSignedUrlConfig } from '@google-cloud/storage';
import { ConfigService } from '@nestjs/config';
import { FileNameService } from './file-name.service';
import { Storage } from '@google-cloud/storage';
import { JwtAuthGuard } from 'src/shared/auth/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('file-import/generate-signed-url')
export class GenerateSignedUrlController {
  constructor(
    private readonly config: ConfigService, // Inject ConfigService for environment variables
    private readonly fileNameService: FileNameService, // Inject FileNameService
  ) {}

  @Get('contact-import')
  async ContactImport(
    @Req() req,
    @Query('wash') washData?: boolean | string,
    @Query('audience') audienceId?: string, 
    @Query('fileName') fileName?: string, 
    @Query('importIntoFlex') importIntoFlex?: boolean | string
  ): Promise<{ signedUrl: string; fileName: string }> {
    const companyId = req.user.companyId; // Get the company ID from the request object
    const userId = req.user.userId; // Get the user ID from the request object

    console.log(`Generating signed URL for contact import with company ID: ${companyId}, audience ID: ${audienceId}, file name: ${fileName}, import into Flex: ${importIntoFlex}, wash data: ${washData}`);
    
    if (typeof washData === 'string') {
      washData = washData.toLowerCase() === 'true';
    }

    if (typeof importIntoFlex === 'string') {
      importIntoFlex = importIntoFlex.toLowerCase() === 'true';
    }

    return this.generateSignedUrl(`contact/${companyId}/${this.fileNameService.generateContactImportFileName(audienceId, fileName, importIntoFlex, washData, userId)}`);
  }

  @Get('campaign-import')
  async CampaignImport(
    @Req() req,
    @Query('dataPrefix') dataPrefix: string,
    @Query('sendDateAsEpoch') sendDateAsEpoch: string,
    @Query('wash') washData?: boolean,
    @Query('templateGroup') templateGroup?: string, 
    @Query('fileName') fileName?: string, 
    @Query('importIntoFlex') importIntoFlex?: boolean,
    @Query('cutOffMinutes') cutOffMinutes?: string,
    @Query('channelId') channelId?: string,
  ): Promise<{ signedUrl: string; fileName: string }> {
    const companyId = req.user.companyId; // Get the company ID from the request object
    const userId = req.user.userId; // Get the user ID from the request object


    if (!sendDateAsEpoch) {
      throw new HttpException('sendDateAsEpoc is required', HttpStatus.BAD_REQUEST);
    }

    if (!templateGroup) {
      throw new HttpException('templateGroup is required', HttpStatus.BAD_REQUEST);
    }

    const sendDate = new Date(Number(sendDateAsEpoch));
    if (isNaN(sendDate.getTime()) || sendDate <= new Date()) {
      throw new HttpException('sendDateAsEpoc must be a valid future UTC timestamp', HttpStatus.BAD_REQUEST);
    }

    console.log(`Generating signed URL for campaign import with company ID: ${companyId}, date as epoch: ${sendDateAsEpoch}, wash data: ${washData}, template group: ${templateGroup}, file name: ${fileName}, import into Flex: ${importIntoFlex}, cut off minutes: ${cutOffMinutes}, channel ID: ${channelId}`);
    return this.generateSignedUrl(`campaign/${companyId}/${this.fileNameService.generagenerateCampaignFileNameteFileName(dataPrefix, importIntoFlex, washData, sendDateAsEpoch, templateGroup, cutOffMinutes, channelId, fileName)}`);
  }

  private async generateSignedUrl(fileName: string) {
    const storage = new Storage();
    const bucketName = this.config.get<string>('GCP_BUCKET_NAME'); // Replace with your bucket name
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName); // Use the file name service to generate the file name

    const options: GetSignedUrlConfig = {
      version: 'v4',
      action: 'write', // Ensure this is explicitly set to a valid action
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
    };

    const url = await file.getSignedUrl(options); // Await the promise and use the first element of the response

    return { signedUrl: url[0], fileName };
  }

}