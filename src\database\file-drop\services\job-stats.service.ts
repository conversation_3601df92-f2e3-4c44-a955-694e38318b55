import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobStats } from '../entities/job-stats.entity';

@Injectable()
export class JobStatsService {

  constructor(
    @InjectRepository(JobStats)
    private readonly jobStatsRepository: Repository<JobStats>,
  ) {}

  async create(jobStats: Partial<JobStats>): Promise<JobStats> {
    const newJobStats = this.jobStatsRepository.create(jobStats);
    return this.jobStatsRepository.save(newJobStats);
  }

  async findAll(): Promise<JobStats[]> {
    return this.jobStatsRepository.find();
  }

  async findOne(id: string): Promise<JobStats | null> {
    return this.jobStatsRepository.findOne({ where: { job_id : id } });
  }

  async update(id: string, jobStats: Partial<JobStats>): Promise<JobStats> {
    await this.jobStatsRepository.update(id, jobStats);
    return this.findOne(id);
  }

  async updateByJobId(jobId: string, jobStats: Partial<JobStats>): Promise<JobStats> {
    await this.jobStatsRepository.update({job_id : jobId}, jobStats);
    return this.jobStatsRepository.findOne({where:{job_id : jobId}});
  }

  async remove(id: string): Promise<void> {
    await this.jobStatsRepository.delete(id);
  }

  async upsert(jobStats: Partial<JobStats>): Promise<JobStats> {
    const restult = await this.jobStatsRepository.upsert(jobStats, ['job_id']);

    return restult.raw[0];
  }

  async updateJobStats(jobId: string): Promise<void> {
    try{
      await this.jobStatsRepository.query('call job_stats_update(?)', [jobId]);
    }
    catch(  error){
      console.error('Error updating job stats:', error);
    }
  }
}