import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { ConfigKeys } from 'src/jobs/company-config/config-keys';
import { CompanyConfigService } from 'src/jobs/company-config/company-config.service';
import { CsvStreamProcessor } from 'src/utils/csv-stream-processors';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { ImportJobDetailFileDto } from './import-job-detail-file.dto';
import { JobDetailsMapper } from './job-details.mapper';
import { ConfigService } from '@nestjs/config';
import { XlsxStreamProcessor } from 'src/utils/xlxs-stream-processors';
import { JobService } from 'src/jobs/jobs/jobs.service';


@Injectable()
export class JobDetailsFileImportService {
  constructor(
    private readonly jobService: JobService,
    private readonly jobDetailsService: JobDetailsService,
    private readonly jobFilesService: JobFilesService,
    private readonly gcpBucketService: GcpBucketService,
    private readonly companyConfigService: CompanyConfigService,
    private readonly csvStreamProcessor: CsvStreamProcessor,
    private readonly xlsxStreamPRocessor : XlsxStreamProcessor,
    private readonly configService: ConfigService
  ) {}
  
  async processFile(companyId: string, fileId: string){
  
    const data = await this.loadFileData(companyId, fileId);
    await this.setCompanyConfigDefaults(data);
    
    // await DtoValidator.validate(data, ImportFile_Process_dto);

    if (data.fileType === 'csv') {
      await this.importCsvFile(data);
    } else if (data.fileType === 'xlsx') {
      await this.importXlsxFile(data);
    } else {
      throw new HttpException('Unsupported file type', HttpStatus.BAD_REQUEST);
    }

    return `File ${data.fileName} processed successfully`;
    }
  
  private async importCsvFile(data: ImportJobDetailFileDto) {
    
    await this.csvStreamProcessor.import(
      data.stream,
      (row, rowIndex) => {
        // Transform row data if needed
        return JobDetailsMapper.createBatchEntry(data.jobId, row, rowIndex, data.numberHeader, data.countryHeader, data.defaultCountry)
      },
      async (batchData) => {
        await this.jobDetailsService.upsertJobDetails(batchData);
      },
      async (headersFromFileStream) => {
        await this.jobService.updateJobHeaderRow(data.jobId, headersFromFileStream);
      }
    );
  }

  private async importXlsxFile(data: ImportJobDetailFileDto) {
    let headers = [];

    this.jobDetailsService.removeAllForJob(data.jobId)

    await this.xlsxStreamPRocessor.import(
      data.stream,
      (row, rowIndex) => {
        // Transform row data if needed
        console.log('Row:', row);

        let rowData = {};
        for (let i = 0; i < headers.length; i++) {
          rowData[headers[i]] = row[i];
        }

        console.log('rowData:', rowData);

        return JobDetailsMapper.createBatchEntry(data.jobId, rowData, rowIndex, data.numberHeader, data.countryHeader, data.defaultCountry)
      },
      async (batchData) => {
        await this.jobDetailsService.upsertJobDetails(batchData);
      },
      async (headersFromFileStream) => {
        headers = headersFromFileStream;

        if (!headers || !headers.includes(data.numberHeader))
        {          
          throw new HttpException(`Mobile Number Header not found in file ${data.numberHeader}`, HttpStatus.BAD_REQUEST);
        }

        await this.jobService.updateJobHeaderRow(data.jobId, headersFromFileStream);
      }
    );
  
  }

  private async setCompanyConfigDefaults(jobData: ImportJobDetailFileDto) {
    console.log(`Company config defaults set: numberHeader=${jobData.numberHeader}, countryHeader=${jobData.countryHeader}, defaultCountry=${jobData.defaultCountry}`);
   
    if (jobData?.countryHeader && jobData?.numberHeader && jobData?.defaultCountry) {
      return;
    }

    const requiredKeys = [ConfigKeys.NumberHeader, ConfigKeys.CountryHeader, ConfigKeys.DefaultCountry];
    const companyConfig = await this.companyConfigService.findByCompanyIdAndKeys(jobData.companyId, requiredKeys);

    if (!companyConfig) {
      throw new HttpException('Company config not found', HttpStatus.NOT_FOUND);
    }
    
    if (!jobData.numberHeader) {
      jobData.numberHeader = companyConfig.find( x => x.key === ConfigKeys.NumberHeader)?.value;

      if (!jobData.numberHeader) {
        jobData.numberHeader = "mobile";
      }
    }
    console.log(`Company config defaults set: numberHeader=${jobData.numberHeader}, countryHeader=${jobData.countryHeader}, defaultCountry=${jobData.defaultCountry}`); 

    if (!jobData.countryHeader) {
      jobData.countryHeader = companyConfig.find( x => x.key === ConfigKeys.CountryHeader)?.value;
    }

    if (!jobData.defaultCountry) {
      jobData.defaultCountry = companyConfig.find( x => x.key === ConfigKeys.DefaultCountry)?.value ?? 'ZA';
    }

    console.log(`Company config defaults set: numberHeader=${jobData.numberHeader}, countryHeader=${jobData.countryHeader}, defaultCountry=${jobData.defaultCountry}`);
  }

  private async loadFileData(companyId, fileId: string) : Promise<ImportJobDetailFileDto> {

    if (!fileId) {
      throw new HttpException('FileId is required', HttpStatus.BAD_REQUEST);
    }

    const file = await this.jobFilesService.findOne(fileId);
    
    if (!file)  {
      console.warn(`File with ID ${fileId} not found, Check file was not soft deleted`);
      throw new HttpException('File not found', HttpStatus.NOT_FOUND);
    }
    
    console.log(JSON.stringify(file));

    const decodedFileName = decodeURIComponent(file.name);

    return {
      companyId: companyId,
      jobId : file.job_id,
      stream : await this.gcpBucketService.getFileStream(this.configService.get<string>('GCP_BUCKET_NAME'), decodedFileName),
      fileType : file.name.endsWith('.csv') ? 'csv' : 'xlsx',
      fileName : file.name,
      numberHeader: '',
      countryHeader: '',
      defaultCountry: '',
    };
  }

}
