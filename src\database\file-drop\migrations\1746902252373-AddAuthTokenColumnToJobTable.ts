import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAuthTokenColumnToJobTable1746902252373 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job
            ADD COLUMN auth_token VARCHAR(5000);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job
            DROP COLUMN auth_token;
        `);
    }
}

