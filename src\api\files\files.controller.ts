import { <PERSON>, Get, Param, Query, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { GcpBucketService } from '../../utils/gcp-bucket.service';
import { ConfigService } from '@nestjs/config';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { JwtAuthGuard } from 'src/shared/auth/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('files')
export class FilesController {
  constructor(private readonly gcpBucketService: GcpBucketService,
    private readonly config: ConfigService,
    private readonly filesService: JobFilesService
  ) {}

  @Get(':fileId')
  async getFile(
    @Param('fileId') fileId: string,
    @Res() res: Response,
    
  ): Promise<void> {
    try {
        const file = await this.filesService.findOne(fileId);

        if (!file) {
          res.status(404).send('File not found');
        }
      
        const bucketName = this.config.get('GCP_BUCKET_NAME');
        const fileStream = await this.gcpBucketService.getFileStream(bucketName, file.name);

      res.setHeader('Content-Disposition', `attachment; filename="${file.name}"`);
      fileStream.pipe(res);
    } catch (error) {
      res.status(500).send('Error retrieving file');
    }
  }
}