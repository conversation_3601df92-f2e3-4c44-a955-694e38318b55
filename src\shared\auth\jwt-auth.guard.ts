import { Injectable, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { JwtService } from '@nestjs/jwt';

const IS_PUBLIC_KEY = 'isPublic';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector, private jwtService: JwtService) {
    super();
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.get<boolean>(IS_PUBLIC_KEY, context.getHandler());
    if (isPublic) return true;

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      // Log the error or reason for rejection
      this.logger.error('Authentication error:', err || info?.message || 'Unknown error');
      throw err || new UnauthorizedException('Unauthorized');
    }

    const request = context.switchToHttp().getRequest();
    request.user = {
      userId: user.sub,
      companyId: user.companyId,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: user.roles,
      channelId: user.channelId,
      prepaid: user.prepaid,
      timezone: user.timezone,
    };

    // Allow expired tokens by ignoring the 'exp' claim
    if (info?.name === 'TokenExpiredError') {
      const token = request.headers.authorization?.split(' ')[1];
      if (token) {
        const decoded = this.jwtService.decode(token);
        if (decoded) {
          this.logger.warn('Token is expired but will be accepted:', decoded);
          request.user = { ...request.user, ...decoded };
          return user;
        }
      }
    }

    return user;
  }
}
