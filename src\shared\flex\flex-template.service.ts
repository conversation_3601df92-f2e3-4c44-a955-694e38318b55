import { Templates } from "src/database/flex/entities/templates.entity";

  export function getTemplateBodyVariables(firstTemplate: Templates) {
    const componentsString = firstTemplate.components;
    const components: any[] = JSON.parse(componentsString);

    const bodyComponent = components.find(
      (c) => c.type?.toUpperCase() === 'BODY'
    );
    // Extract all numbers inside the pattern {{Number}} that can appear anywhere in the text
    const bodyText = bodyComponent?.text || '';
    const matches = bodyText.match(/{{(\d+)}}/g) || [];
    const templateBodyVariables = matches.map((match) => match.replace(/[^\d]/g, '')
    );
    return templateBodyVariables;
  }

  export function getTemplateBodyVariableExamples(firstTemplate: Templates) : string[]
  {
    const componentsString = firstTemplate.components;
    const components: any[] = firstTemplate ? JSON.parse(componentsString) : [];
    const bodyComponent = components.find(
    (c) => c.type?.toUpperCase() === 'BODY',
    );
    const examples = (bodyComponent?.example as string[]) || [];
    return examples;
  }