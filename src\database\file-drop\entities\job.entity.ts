import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToOne, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { JobType } from './job-type.entity';
import { JobStatus } from './job-status.entity';
import { ActivityLog } from './activity-log.entity';
import { JobDetail } from './job-detail.entity';
import { JobStats } from './job-stats.entity';
import { JobAction } from './job-action.entity';
import { JobFiles } from './job-files.entity';

@Entity('job')
export class Job {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  name: string;

  @Column({ type: 'bigint', nullable: true })
  company_id: string;

  @Column({ type: 'int', nullable: true })
  upload_source: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  original_file_name: string;

  @Column({ type: 'datetime', nullable: true })
  start_time: Date;

  @Column({ type: 'datetime', nullable: true })
  end_time: Date;

  @Column({ type: 'bigint', nullable: true })
  job_type_id: string;

  @Column({ type: 'bigint', nullable: true })
  status_id: string;

  @Column({ type: 'bigint', nullable: true })
  job_sub_status_id: string;

  @Column({ type: 'bigint' })
  created_by_user_id: string;

  @Column({ type: 'bigint' })
  updated_by_user_id: string;

  @CreateDateColumn({ type: 'datetime' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deleted_at: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  file_headers: string;

  @Column({ type: 'datetime', nullable: true })
  cutoff_time: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  template_name: string;

  @Column({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP' })
  last_updated: Date;

  @Column({ type: 'varchar', length: 5000, nullable: true })
  auth_token: string;

  @Column({ type: 'json', nullable: true })
  job_data: any;

  @Column({ type: 'int', nullable: true })
  percentage_complete: number;

  @ManyToOne(() => JobType, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'job_type_id' })
  jobType: JobType;

  @ManyToOne(() => JobStatus, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'status_id' })
  jobStatus: JobStatus;

  @OneToMany(() => ActivityLog, (activityLog) => activityLog.job, { cascade: true })
  activityLogs: ActivityLog[];

  @OneToMany(() => JobAction, (action) => action.job, { cascade: true })
  actions: JobAction[];

  @OneToMany(() => JobDetail, (jobDetail) => jobDetail.job, { cascade: true })
  jobDetails: JobDetail[];

  @OneToOne(() => JobStats, { cascade: true })
  @JoinColumn({ name: 'id', referencedColumnName: 'job_id' })
  jobStats: JobStats;

  
  @OneToOne(() => JobFiles, { cascade: true })
  @JoinColumn({ name: 'id', referencedColumnName: 'job_id' })
  files: JobFiles;

}