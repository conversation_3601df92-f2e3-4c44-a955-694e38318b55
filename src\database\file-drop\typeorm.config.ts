// src/config/typeorm.config.ts
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { LoggerService } from '../../utils/logger.service'; // Updated to use a relative import
import { DataSource } from 'typeorm';

export const buildTypeOrmConfig = (config: ConfigService): TypeOrmModuleOptions => 
  {
    const output = {
    type: 'mysql' as const,
    host:     config.get('FILE_DROP_DB_HOST', 'localhost'),
    port:     config.get<number>('FILE_DROP_DB_PORT', 3306),
    username: config.get('FILE_DROP_DB_USERNAME'),
    password: config.get('FILE_DROP_DB_PASSWORD'),
    database: config.get('FILE_DROP_DB_DATABASE'),
    entities: [__dirname + '/../**/file-drop/**/*.entity.{js,ts}'],
    migrations: [__dirname + '/../**/migrations/*{.ts,.js}'],

    synchronize: false,
    logging:     config.get('FILE_DROP_DB_LOGGING') === 'true',
    autoLoadEntities: true,
    migrationsRun: true,
  };

  LoggerService.debug('config', JSON.stringify(output));

  return output;
};

export const fileDropDataSource = new DataSource({
  type: 'mysql',
  host: process.env.FILE_DROP_DB_HOST,
  port: parseInt(process.env.FILE_DROP_DB_PORT || '3306', 10),
  username: process.env.FILE_DROP_DB_USERNAME,
  password: process.env.FILE_DROP_DB_PASSWORD,
  database: process.env.FILE_DROP_DB_DATABASE,
  entities: [__dirname + '/../**/file-drop/*.entity{.ts,.js}'],
  migrations: [__dirname + './../**/migrations/*{.ts,.js}'],
  synchronize: false,
  migrationsRun: true,
});


