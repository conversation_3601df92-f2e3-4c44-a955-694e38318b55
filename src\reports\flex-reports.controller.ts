import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { Job } from '../database/file-drop/entities/job.entity';
import { ListResponseDto } from 'src/shared/dtos/list-resposne.dto';
import { FlexImportJobDto } from './dtos/flex-import-job.dto';
import { query } from 'express';
import { JwtAuthGuard } from 'src/shared/auth/jwt-auth.guard';
import { ApiResourceListDto } from 'src/shared/dtos/ApiResourceListDto';
import { plainToInstance } from 'class-transformer';
import { ConfigService } from '@nestjs/config';
import { JobStatus, JobSubStatusEnum } from 'src/database/file-drop/entities/enums';


@UseGuards(JwtAuthGuard)
@Controller('flex-reports')
export class FlexReportsController {
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly config: ConfigService,
  ) {}

  @Get('contact-import')
  async getJobsByCompany(@Req() req, @Query() query: any): Promise<ListResponseDto<FlexImportJobDto>> {
    if (typeof query.filters === 'string') {
      query.filters = JSON.parse(query.filters);
    }
    if (typeof query.sort === 'string') {
        query.sort = JSON.parse(query.sort);
      }
    //const transformedQuery = plainToInstance(ApiResourceListDto, query);
    const companyId = req.user.companyId;
    const skip = (query.pageNo - 1) * query.pageSize;
    const filters = { company_id: companyId };
    const sortfilter = {};
    const baseUrl =  this.config.get('API_BASE_URL');

    console.log(query);

    query?.filters?.forEach(filter => {
      if (filter.field === 'fileName') {
        filters['original_file_name'] = Like('%' + filter.value + '%');
      }
    });
    
    query?.sort?.forEach(sort => {
        if (sort.field === 'createdAt') {
            sortfilter['created_at'] = sort.sortOrder;
        }
    });

    const totalResults = await this.jobRepository.count({
      where: filters,
    });

    const results = await this.jobRepository.find({
      where: filters,
      relations: ['jobStats'],
      skip,
      take: query.pageSize,
    });

    const mappedResults = results.map(x => ({
      id: x.id,
      fileName: x.original_file_name.split('_').pop(),
      status: x.jobStats?.display_status ?? JobSubStatusEnum[x.job_sub_status_id] ?? JobStatus[x.status_id],
      percentageComplete: x.percentage_complete?.toString(),
      failureCount: 0,
      successCount: 0,
      duplicateCount: 0,
      failedFileUrl:  x.jobStats?.failed_file_url,
      successFileUrl: x.jobStats?.success_file_url,
      failureFileName: x.jobStats?.failed_file_name,
      processingStartDate: x.start_time?.toISOString(),
      processingEndDate: x.end_time?.toISOString(),
      audience: {
        id: x.jobStats?.audience?.id,
        name: x.jobStats?.audience?.name,
      },
      updatedBy: {
        id: x.jobStats?.created_by?.id,
        firstName: x.jobStats?.created_by?.firstName,
        lastName: x.jobStats?.created_by?.lastName,
      },
      createdAt: x.created_at?.toISOString(),
      updatedAt: x.updated_at?.toISOString(),
    }));

    return new ListResponseDto<FlexImportJobDto>(
        query.pageSize,
        query.pageNo,
      totalResults,
      mappedResults,
    );
  }
}