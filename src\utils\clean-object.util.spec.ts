import { CleanObjectUtil } from './clean-object.util';

describe('CleanObjectUtil', () => {
  it('should remove null, undefined, and blank string properties from an object', () => {
    const input = {
      key1: 'value1',
      key2: null,
      key3: undefined,
      key4: '',
      key5: 'value5',
    };

    CleanObjectUtil.cleanObject(input);

    expect(input).toEqual({
      key1: 'value1',
      key5: 'value5',
    });
  });

  it('should handle an empty object', () => {
    const input = {};

    CleanObjectUtil.cleanObject(input);

    expect(input).toEqual({});
  });

  it('should handle an object with no removable properties', () => {
    const input = {
      key1: 'value1',
      key2: 123,
      key3: true,
    };

    CleanObjectUtil.cleanObject(input);

    expect(input).toEqual({
      key1: 'value1',
      key2: 123,
      key3: true,
    });
  });

  it('should handle nested objects by not cleaning them recursively', () => {
    const input = {
      key1: 'value1',
      key2: {
        nestedKey1: null,
        nestedKey2: 'nestedValue2',
      },
      key3: '',
    };

    CleanObjectUtil.cleanObject(input);

    expect(input).toEqual({
      key1: 'value1',
      key2: {
        nestedKey1: null,
        nestedKey2: 'nestedValue2',
      },
    });
  });
});