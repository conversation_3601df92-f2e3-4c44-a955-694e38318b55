import { MigrationInterface, QueryRunner } from "typeorm";

export class JobActionAddRepeatColumn1748384467295 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action ADD COLUMN repeat_after_x_seconds INT`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE job_action DROP COLUMN repeat_after_x_seconds`);
    }

}
