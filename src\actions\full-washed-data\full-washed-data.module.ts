import { Modu<PERSON> } from '@nestjs/common';
import { FullWashedDataController } from './full-washed-data.controller';
import { FullWashedDataService } from './full-washed-data.service';
import { GcpBucketService } from 'src/utils/gcp-bucket.service';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobDetail } from 'src/database/file-drop/entities/job-detail.entity';
import { Job } from 'src/database/file-drop/entities/job.entity';
import { JobsModule } from 'src/jobs/jobs/jobs.module';

@Module({
  imports: [TypeOrmModule.forFeature([JobDetail, Job]), JobsModule],
  controllers: [FullWashedDataController],
  providers: [FullWashedDataService, GcpBucketService, JobDetailsService],
})
export class FullWashedDataModule {}