import {
    <PERSON><PERSON>ty,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    CreateDateColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { TemplateGroup } from './template-groups.entity';
  import { Templates } from './templates.entity';
  
  @Entity('template_group_templates')
  export class TemplateGroupTemplate {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: string;
  
    @ManyToOne(() => TemplateGroup, (templateGroup) => templateGroup.templateGroupTemplates, { onDelete: 'CASCADE' })
    templateGroup: TemplateGroup;
  
    @ManyToOne(() => Templates, (template) => template.templateGroupTemplates, { onDelete: 'CASCADE' })
    template: Templates;
  
    @Column({ type: 'bigint', unsigned: true, default: 0 })
    usageCount: number;
  
    @CreateDateColumn({ type: 'timestamp', name: 'created_at', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
  
    @UpdateDateColumn({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
  }