import { Controller, Post, Param, UseGuards } from '@nestjs/common';
import { DetectDuplicatesService } from './detect-duplicates.service';
import { ActionProcessingService } from 'src/engine/action-processing.service';
import { CombinedAuthGuard } from 'src/shared/auth/combined.auth.guard';
import { JobSubStatusEnum } from 'src/database/file-drop/entities/enums';

@UseGuards(CombinedAuthGuard)
@Controller('actions/:actionId/detect-duplicates')
export class DetectDuplicatesController {
  constructor(private readonly detectDuplicatesService: DetectDuplicatesService,
        private readonly actionProcessingService: ActionProcessingService,
  ) {}

  @Post('')
  async processDuplicates(@Param('actionId') actionId: string): Promise<void> {
    
    await this.actionProcessingService.process(actionId, async (action) => {
      await this.detectDuplicatesService.processDuplicates(action.job_id);
    },JobSubStatusEnum['Validating Data']); 
   
  }
}