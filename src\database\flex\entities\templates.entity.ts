import {
  Column,
  <PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
  ManyToOne,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from 'typeorm';
import { TemplateGroup } from './template-groups.entity';
import { TemplateGroupTemplate } from './template-group-template.entity';


@Entity('templates')
export class Templates {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('text', { name: 'name' })
  name: string;

  @Column('longtext', { name: 'components', nullable: true })
  components: string | null;

  @Column('varchar', { name: 'language', length: 10 })
  language: string;

  @Column('text', { name: 'category' })
  category: string;

  @Column('bigint', { name: 'provider_template_id', nullable: true, unsigned: true })
  providerTemplateId: bigint | null;

  @Column('bigint', { name: 'channel_id', nullable: true, unsigned: true })
  channelId: string | null;

  @Column('text', { name: 'friendly_name', nullable: true })
  friendlyName: string | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('tinyint', { name: 'removed', width: 1, default: () => 0 })
  removed: boolean;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('text', { name: 'error_code', nullable: true })
  errorCode: string | null;

  @Column('text', { name: 'error_text', nullable: true })
  errorText: string | null;

  @Column('int', { name: 'campaign_template_type_id', nullable: true })
  campaignTemplateTypeId: bigint | null;

  @Column('tinyint', { name: 'payment_template', default: () => 0 })
  paymentTemplate: boolean;

  @Column('varchar', { name: 'WABA', length: 500, nullable: true })
  WABA: string | null;

  @Column('bigint', { name: 'status_id', nullable: true, unsigned: true })
  statusId: bigint | null;

  @Column({ name: 'is_hidden', default: false, nullable: false })
  isHidden: boolean;

  @Column({ name: 'is_system_template', default: false, nullable: false })
  isSystemTemplate: boolean;

  @Column('text', { name: 'bot_id', nullable: true })
  botId: string | null;

  @Column({ name: 'has_cta', default: false, nullable: false })
  hasCTA: boolean;

  @Column({ name: 'has_qr', default: false, nullable: false })
  hasQR: boolean;

  @Column({ name: 'has_media', default: false, nullable: false })
  hasMedia: boolean;

  @Column({ name: 'has_variables', default: false, nullable: false })
  hasVariables: boolean;

  @ManyToMany(() => TemplateGroup, (templateGroup) => templateGroup.templates)
  templateGroups: TemplateGroup[];

  @OneToMany(() => TemplateGroupTemplate, (tgt) => tgt.template, { cascade: true })
  templateGroupTemplates: TemplateGroupTemplate[];
}
