import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactWasherApiCompletedFilesService } from './contact-washer-api-completed-files.service';
import { ContactWasherApiCompletedFilesController } from './contact-washer-api-completed-files.controller';
import { ContactWasherApiCompletedFiles } from '../../database/file-drop/entities/contact-washer-api-completed-files.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ContactWasherApiCompletedFiles])],
  controllers: [ContactWasherApiCompletedFilesController],
  providers: [ContactWasherApiCompletedFilesService],
  exports: [ContactWasherApiCompletedFilesService],
})
export class ContactWasherApiCompletedFilesModule {}