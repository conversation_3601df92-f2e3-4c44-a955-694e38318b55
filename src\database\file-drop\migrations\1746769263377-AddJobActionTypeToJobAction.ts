import { query } from "express";
import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobActionTypeToJobAction1746769263377 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action ADD COLUMN job_action_type_id BIGINT UNSIGNED DEFAULT NULL;
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE job_action DROP COLUMN job_action_type_id;
            `
        );
    }

}
