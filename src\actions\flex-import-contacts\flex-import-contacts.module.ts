import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { EngineModule } from 'src/engine/engine.module';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';
import { FlexImportContactsController } from './flex-import-contacts.controller';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { UtilModule } from 'src/utils/util.module';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';

@Module({
      imports: [DatabaseModule, EngineModule, FlexDatabaseModule, UtilModule, JobFilesModule],
  controllers: [FlexImportContactsController],
  providers: [JobDetailsService,JobFilesService],
})
export class FlexImportContactsModule {}