import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Campaign } from '../entities/campaigns.entity';
import { ReportDataDto } from '../dto/report-data.dto';

@Injectable()
export class CampaignService {

  private readonly audienceRepository: Repository<Campaign>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.audienceRepository = this.flexDataSource.getRepository(Campaign);
  }

  async create(campaign: Partial<Campaign>): Promise<Campaign> {
    return this.audienceRepository.save(campaign);
  }

  async findOneByName(name: string, companyId: string): Promise<Campaign | null> {
    return this.audienceRepository.findOne({ where: {name, companyId } });
  }

  async InsertMessagesForCampaign(campaignId : string) : Promise<any> {
    return await this.flexDataSource.query(
      'CALL insert_messages_for_campaign(?)',
      [campaignId],
    );
  }

  async getReportData(campaignId: string, updatesAfter: Date, limit: Number): Promise<ReportDataDto[]> {
    const result = await this.flexDataSource.query(
      'CALL campaign_send_report(?, ?, ?)',
      [campaignId, updatesAfter, limit],
    );

    console.log(`CALL campaign_send_report(${campaignId}, ${updatesAfter}, ${limit})`)

    return result[0];
  }
}