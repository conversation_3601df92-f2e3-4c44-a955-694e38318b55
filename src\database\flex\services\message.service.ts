import { Injectable, Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Messages } from '../entities/messages.entity';

@Injectable()
export class MessageService {

  private readonly messageRepository: Repository<Messages>;

  constructor(
    @Inject('FLEX_DATABASE') private readonly flexDataSource: DataSource,
  ) {
    this.messageRepository = this.flexDataSource.getRepository(Messages);
  }

  async upsert(message : Partial<Messages>): Promise<Messages | null> {
    const response = await this.messageRepository.upsert(message, ['campaignId', 'contactId']);
    // After upsert, fetch the entity by unique keys to get the id
    const updatedMessage = await this.messageRepository.findOne({
      where: {
      campaignId: message.campaignId,
      contactId: message.contactId,
      },
    });

    return updatedMessage;
  }
}

