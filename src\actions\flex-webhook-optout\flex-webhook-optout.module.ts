import { Modu<PERSON> } from '@nestjs/common';

import { DatabaseModule } from 'src/database/file-drop/filedropdatabase.module';
import { EngineModule } from 'src/engine/engine.module';
import { FlexDatabaseModule } from 'src/database/flex/flex-database.module';
import { JobDetailsService } from 'src/jobs/job-details/job-details.service';
import { UtilModule } from 'src/utils/util.module';
import { JobFilesService } from 'src/jobs/job-files/job-files.service';
import { JobFilesModule } from 'src/jobs/job-files/job-files.module';
import { FlexWebhookOptOutController } from './flex-webhook-optout.controller';

@Module({
      imports: [DatabaseModule, EngineModule, FlexDatabaseModule, UtilModule, JobFilesModule],
  controllers: [FlexWebhookOptOutController],
  providers: [JobDetailsService,JobFilesService],
})
export class FlexWebhookOptoutModule {}